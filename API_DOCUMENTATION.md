# Vyoo Streaming Server - API Documentation

This document provides a detailed overview of the API endpoints available in the Vyoo Streaming Server.

## Base URL

All API endpoints are prefixed with: `/v1/api`

## Authentication

-   Endpoints marked with `🔒` require authentication.
-   Authentication is handled via a JWT `Bearer` token in the `Authorization` header.
-   `Authorization: Bearer <your_jwt_token>`

---

## 🚦 RTMP Authentication Endpoints

These endpoints are primarily used by the RTMP server (Live2) to authenticate streams.

-   **Base Path**: `/v1/api/rtmp-auth`

| Method | Endpoint         | Description                                        | Auth     |
| :----- | :--------------- | :------------------------------------------------- | :------- |
| `POST` | `/auth-key`      | Authenticates a stream key when a stream starts.   | Public   |
| `POST` | `/auth-user`     | Authenticates a user for streaming.                | Public   |
| `POST` | `/auth-record`   | Authenticates the start of a stream recording.     | Public   |
| `POST` | `/deauth-user`   | De-authenticates a user when a stream ends.        | Public   |
| `POST` | `/deauth-record` | De-authenticates a recording when it ends.         | Public   |

---

## 📺 Stream Management Endpoints

-   **Base Path**: `/v1/api/stream`

| Method | Endpoint                        | Description                                          | Auth |
| :----- | :------------------------------ | :--------------------------------------------------- | :--- |
| `GET`  | `/health`                       | Public health check for the streaming service.       | 🔒   |
| `GET`  | `/`                             | Fetch a list of streams.                             | 🔒   |
| `POST` | `/`                             | Create a new stream.                                 | 🔒   |
| `GET`  | `/:id`                          | Get details for a specific stream.                   | 🔒   |
| `PUT`  | `/:id`                          | Update a stream's settings.                          | 🔒   |
| `DELETE`| `/:id`                         | Delete a stream.                                     | 🔒   |
| `GET`  | `/is-live/:streamId?`           | Check if a stream is currently live.                 | 🔒   |
| `GET`  | `/urls/:streamKey`              | Get the RTMP and HLS URLs for a stream.              | 🔒   |
| `GET`  | `/stats`                        | Get streaming server statistics.                     | 🔒   |
| `POST` | `/comments`                     | Fetch comments for a stream.                         | 🔒   |
| `POST` | `/report-stream`                | Report a stream.                                     | 🔒   |

---

## 👤 User & Authentication Endpoints

-   **Base Path**: `/v1/api/auth`, `/v1/api/users`

| Method | Endpoint                   | Description                                          | Auth     |
| :----- | :------------------------- | :--------------------------------------------------- | :------- |
| `POST` | `/auth/register`           | Register a new user.                                 | Public   |
| `POST` | `/auth/login`              | Log in a user with email and password.               | Public   |
| `GET`  | `/auth/google`             | Initiate Google OAuth2 login.                        | Public   |
| `GET`  | `/auth/google/callback`    | Callback for Google OAuth2.                          | Public   |
| `GET`  | `/users/me`                | Get the profile of the currently logged-in user.     | 🔒       |
| `PUT`  | `/users/me`                | Update the profile of the currently logged-in user.  | 🔒       |

---

## ✨ Other Endpoints

The API also includes endpoints for:
-   **Search**: `/v1/api/search`
-   **Shorts**: `/v1/api/shorts`
-   **Stories**: `/v1/api/story`
-   **Playlists**: `/v1/api/playlist`
-   **Notifications**: `/v1/api/notification`
-   **Stripe Payments**: `/v1/api/stripe`

Please refer to the route definitions in `src/routes/` for more details on these endpoints. 