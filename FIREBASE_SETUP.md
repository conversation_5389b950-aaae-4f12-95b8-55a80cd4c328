# Firebase Configuration Setup

## Overview
Firebase credentials have been moved to environment variables for security. You need to set the following environment variables in your `.env` file.

## Required Environment Variables

```env
# Firebase Service Account Configuration
FIREBASE_PROJECT_ID=ai-metastart-vyoo-3ec52
FIREBASE_PRIVATE_KEY_ID=your_private_key_id_here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_client_id_here
```

## How to Get Firebase Credentials

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `ai-metastart-vyoo-3ec52`
3. Navigate to **Project Settings** > **Service Accounts**
4. Click **Generate new private key**
5. Download the JSON file
6. Extract the values and add them to your `.env` file

## Important Notes

- **Never commit the service account JSON file to version control**
- The `FIREBASE_PRIVATE_KEY` should include the full private key with `\n` for line breaks
- Make sure all environment variables are set before starting the application
- The application will throw an error on startup if any required Firebase variables are missing

## Security
- Keep your `.env` file local and never commit it to version control
- Use different service accounts for different environments (development, staging, production)
- Regularly rotate your Firebase service account keys for security 