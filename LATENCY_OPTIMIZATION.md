# Vyoo Streaming Platform - Latency Optimization Guide

## Overview

This document outlines the comprehensive latency optimizations implemented in the Vyoo streaming platform to achieve ultra-low latency RTMP/HLS streaming. The optimizations target end-to-end latency reduction from RTMP ingest to HLS playback while maintaining stream stability and quality.

## Target Latency Goals

- **Ultra-Low Latency**: < 2 seconds end-to-end
- **Low Latency**: < 3 seconds end-to-end  
- **Acceptable Latency**: < 5 seconds end-to-end

## Architecture Overview

The Vyoo platform uses **OvenMediaEngine (OME)** with Low-Latency HLS (LLHLS) for optimal streaming performance:

- **RTMP Input**: Port 1935 (optimized for low-latency ingest)
- **LLHLS Output**: Port 8080 (HTTP/1.1) and 8443 (HTTP/2 with TLS)
- **WebRTC**: Ports 3333/3334 (for ultra-low latency when needed)
- **API Server**: Port 8081 (stream lifecycle and monitoring)

## Key Optimizations Implemented

### 1. LLHLS Configuration Optimizations

#### Segment and Chunk Settings
```xml
<!-- Ultra-low latency configuration -->
<ChunkDuration>0.1</ChunkDuration>          <!-- Reduced from 0.2s to 0.1s -->
<PartHoldBack>0.3</PartHoldBack>            <!-- Reduced from 1s to 0.3s -->
<SegmentDuration>1</SegmentDuration>        <!-- Reduced from 3s to 1s -->
<SegmentCount>10</SegmentCount>             <!-- Increased from 6 to 10 -->
```

**Impact**: Reduces buffering time and enables faster playlist updates

#### Worker Process Optimization
```xml
<RTMP>
    <WorkerCount>4</WorkerCount>            <!-- Increased from 1 to 4 -->
</RTMP>
<LLHLS>
    <WorkerCount>4</WorkerCount>            <!-- Increased from 1 to 4 -->
</LLHLS>
<Publishers>
    <AppWorkerCount>2</AppWorkerCount>      <!-- Increased from 1 to 2 -->
    <StreamWorkerCount>12</StreamWorkerCount> <!-- Increased from 8 to 12 -->
</Publishers>
```

**Impact**: Better parallel processing and reduced processing delays

### 2. Hardware Acceleration

```xml
<HWAccels>
    <Decoder>
        <Enable>true</Enable>
        <Modules>nv:0,qsv:0</Modules>      <!-- NVIDIA + Intel QSV -->
    </Decoder>
    <Encoder>
        <Enable>true</Enable>
        <Modules>nv:0,qsv:0</Modules>      <!-- Hardware encoding -->
    </Encoder>
</HWAccels>
```

**Impact**: Reduces CPU load and encoding latency

### 3. Network and System Optimizations

#### Docker Configuration
```yaml
deploy:
  resources:
    limits:
      memory: 12G                          # Increased from 8G
      cpus: '8.0'                         # Increased from 6.0
    reservations:
      memory: 4G                          # Increased from 2G
      cpus: '4.0'                         # Increased from 3.0

sysctls:
  - net.core.somaxconn=65535
  - net.core.rmem_max=134217728           # Increased receive buffer
  - net.core.wmem_max=134217728           # Increased send buffer
  - net.ipv4.tcp_rmem=4096 87380 134217728
  - net.ipv4.tcp_wmem=4096 65536 134217728
  - net.ipv4.tcp_congestion_control=bbr   # BBR congestion control
```

**Impact**: Better network performance and reduced packet loss

### 4. Stream Lifecycle Optimization

#### Webhook Timeout Reduction
```xml
<AdmissionWebhooks>
    <Timeout>2000</Timeout>               <!-- Reduced from 5000ms to 2000ms -->
</AdmissionWebhooks>
```

**Impact**: Faster stream start/stop processing

## Monitoring and Measurement

### 1. Latency Monitoring Service

The platform includes comprehensive latency monitoring:

```typescript
// Real-time latency measurement
const metrics = await latencyMonitorService.measureStreamLatency(streamKey);

// Metrics include:
// - RTMP ingest latency
// - Segment generation latency  
// - Playlist update latency
// - End-to-end latency
// - Buffer health
```

### 2. Performance Monitoring

```typescript
// System and stream performance monitoring
const performanceMetrics = await performanceMonitorService.collectStreamMetrics();

// Includes:
// - CPU/Memory/Disk usage
// - Network bandwidth
// - Stream viewer counts
// - Buffer health
// - Alert system
```

### 3. Network Resilience

```typescript
// Adaptive streaming based on network conditions
networkResilienceService.startNetworkMonitoring();

// Features:
// - Network condition detection
// - Automatic stream recovery
// - Adaptive bitrate adjustments
// - Connection failure handling
```

## API Endpoints

### Mobile-Optimized Latency Monitoring
- `GET /v1/api/stream/mobile/latency-test/:streamKey` - Mobile-specific latency test
- `GET /v1/api/stream/mobile/config` - Get mobile streaming configuration
- `GET /v1/api/stream/mobile/urls/:streamKey` - Get mobile-optimized stream URLs
- `GET /v1/api/stream/latency/metrics/:streamKey` - Get latency metrics for a stream
- `GET /v1/api/stream/latency/metrics` - Get metrics for all active streams
- `GET /v1/api/stream/latency/history/:streamKey` - Get latency history
- `POST /v1/api/stream/latency/monitoring/start` - Start latency monitoring
- `POST /v1/api/stream/latency/monitoring/stop` - Stop latency monitoring

### Stream Status
- `GET /v1/api/stream/is-live/:streamKey` - Check if stream is live
- `GET /v1/api/stream/health` - Health check for streaming infrastructure

## Testing and Validation

### Mobile-Specific Testing Scripts

Run the mobile-optimized latency test:

```bash
./scripts/test-mobile-latency.sh
```

The script performs:
- Mobile-specific dependency checks
- Service health verification
- Mobile-optimized test stream generation
- Mobile latency measurements with stricter thresholds
- LLHLS mobile optimization validation
- Mobile API endpoint testing
- Mobile performance benchmarking

Run the comprehensive latency test:

```bash
./scripts/test-latency-optimization.sh
```

### Expected Results

With mobile-specific optimizations applied:
- **Excellent Mobile Performance**: < 1500ms average latency
- **Good Mobile Performance**: < 2500ms average latency
- **Acceptable Mobile Performance**: < 4000ms average latency

Mobile-specific improvements:
- Reduced segment count (6 instead of 10)
- Optimized chunk duration (0.1s)
- Minimal buffer size for mobile networks
- No browser fallback overhead

## Quality Levels Supported

The platform maintains support for multiple quality levels without latency impact:

- **4K (3840x2160)**: Ultra High Quality
- **2K (2560x1440)**: High Quality  
- **1080p (1920x1080)**: Standard Quality (active by default)

Quality levels can be enabled by uncommenting profiles in `live2/ome_conf/Server.xml`.

## Mobile-Only Deployment Optimizations

### Mobile App Compatibility
- **Android**: Native LLHLS support through ExoPlayer
- **iOS**: Native LLHLS support through AVPlayer
- **React Native**: Optimized for both platforms

### Mobile-Specific Optimizations
- **LLHLS Only**: No browser fallbacks needed for mobile apps
- **Reduced Segments**: 6 segments instead of 10 for faster startup
- **Optimized Buffering**: Minimal buffer size for mobile networks
- **No WebRTC**: Removed WebRTC fallback - mobile apps use LLHLS directly

## Troubleshooting

### High Latency Issues

1. **Check Network Conditions**
   ```bash
   curl http://localhost:8081/v1/api/stream/latency/metrics
   ```

2. **Verify LLHLS Configuration**
   ```bash
   curl http://localhost:8080/app/STREAM_KEY/llhls.m3u8
   ```

3. **Monitor System Resources**
   ```bash
   docker stats rtmp-server
   ```

### Common Issues

#### Latency > 5 seconds
- Check network bandwidth
- Verify hardware acceleration is working
- Monitor CPU/memory usage
- Check for packet loss

#### Stream Disconnections
- Review network resilience logs
- Check RTMP connection stability
- Verify webhook response times

#### Buffer Underruns
- Increase segment count
- Check encoding performance
- Monitor disk I/O

## Performance Tuning

### For Higher Concurrency
```xml
<StreamWorkerCount>16</StreamWorkerCount>
<AppWorkerCount>4</AppWorkerCount>
```

### For Lower Latency (Experimental)
```xml
<ChunkDuration>0.05</ChunkDuration>
<PartHoldBack>0.2</PartHoldBack>
<SegmentDuration>0.5</SegmentDuration>
```

**Warning**: Very low settings may impact stability

### For Better Stability
```xml
<ChunkDuration>0.2</ChunkDuration>
<PartHoldBack>0.5</PartHoldBack>
<SegmentDuration>2</SegmentDuration>
<SegmentCount>8</SegmentCount>
```

## Monitoring Dashboard

Access real-time metrics:
- Latency metrics: `http://localhost:8081/v1/api/stream/latency/metrics`
- Stream statistics: `http://localhost:8080/stat`
- Health status: `http://localhost:8081/v1/api/stream/health`

## Future Enhancements

1. **WebRTC Integration**: For sub-second latency
2. **CDN Integration**: For global low-latency delivery
3. **AI-Powered Optimization**: Dynamic parameter tuning
4. **Advanced Analytics**: Detailed latency breakdown
5. **Multi-CDN Failover**: Automatic CDN switching

## Conclusion

The implemented optimizations significantly reduce streaming latency while maintaining stability and quality. The comprehensive monitoring system ensures optimal performance and quick issue detection. Regular testing with the provided script validates the effectiveness of the optimizations.

For support or questions, refer to the monitoring APIs and logs for detailed performance insights.
