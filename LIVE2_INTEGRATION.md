# Live2 Streaming Container Integration

This document explains the integration of the Live2 RTMP/HLS streaming container with your Vyoo JavaScript server.

## 🎯 Overview

The Live2 container provides a high-performance, production-ready streaming infrastructure that replaces your current streaming setup with:

- **Real-time RTMP ingestion** on port 1935
- **Multi-quality HLS streaming** on port 8080  
- **Adaptive bitrate streaming** with 3 quality levels
- **Low-latency optimizations** for live streaming
- **Built-in monitoring and statistics**

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamer      │    │   Live2         │    │   Viewers       │
│   (OBS/FFmpeg)  │───▶│   Container     │───▶│   (HLS Players) │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
   RTMP Stream              Multi-Quality            Adaptive
   rtmp://host:1935         Transcoding              Streaming
   /live/stream_key         (4K, 2K, 1080p)         Selection
```

## 🚀 Quick Start

### 1. Start the Complete System

```bash
# Build and start both containers
npm run start:live2

# Or manually with the script
./scripts/start-with-live2.sh
```

### 2. Stream to the Server

```bash
# Test stream with FFmpeg
ffmpeg -re -f lavfi -i testsrc2=size=1280x720:rate=30 \
  -c:v libx264 -preset ultrafast -f flv \
  rtmp://localhost:1935/live/test_stream
```

### 3. Watch the Stream

Open in any HLS-compatible player:
- Master playlist: `http://localhost:8080/hls/test_stream.m3u8`
- 4K quality: `http://localhost:8080/hls/test_stream_4k.m3u8`
- 2K quality: `http://localhost:8080/hls/test_stream_2k.m3u8`
- 1080p quality: `http://localhost:8080/hls/test_stream_1080.m3u8`

## 📡 Streaming Features

### Quality Levels
- **4K (3840x2160)**: 10M bitrate, 128k audio
- **2K (2560x1440)**: 5M bitrate, 128k audio  
- **1080p (1920x1080)**: 2M bitrate, 128k audio

### Performance Optimizations
- **Low Latency**: 2-second HLS fragments
- **Fast Encoding**: `veryfast` preset with `zerolatency` tuning
- **Optimized Keyframes**: 30-frame GOP for consistent quality
- **Auto Cleanup**: Removes old segments automatically

### Adaptive Streaming
- Automatic quality switching based on bandwidth
- Seamless transitions between quality levels
- Client-side quality selection support

## 🔌 API Integration

### New Endpoints Available

#### Get Stream URLs
```http
GET /v1/api/stream/urls/:streamKey
```
Returns RTMP and HLS URLs for a stream with all quality options.

#### Streaming Statistics  
```http
GET /v1/api/stream/stats
```
Real-time statistics from the Live2 container including viewer counts and bandwidth.

#### Health Check
```http
GET /v1/api/stream/health
```
Health status of the streaming infrastructure.

#### Enhanced Stream Status
```http
GET /v1/api/stream/live/:streamId
```
Now checks Live2 container first for real-time status.

### Service Integration

The `Live2Service` class provides:

```typescript
// Check if stream is live
const isLive = await live2Service.isStreamLive(streamKey);

// Get viewer count
const viewers = await live2Service.getViewerCount(streamKey);

// Generate streaming URLs
const hlsUrl = live2Service.getMasterPlaylistUrl(streamKey);
const rtmpUrl = live2Service.generateRTMPUrl(streamKey);

// Get statistics
const stats = await live2Service.getStreamingStats();
```

## 📊 Monitoring

### Built-in Statistics
Visit `http://localhost:8080/stat` for real-time XML statistics including:
- Active streams
- Viewer counts
- Bandwidth usage
- Connection statistics

### Interactive Monitoring
```bash
# Live2 monitoring dashboard
cd live2 && ./monitor.sh

# Docker logs
docker-compose logs -f rtmp-server

# Container resource usage
docker stats rtmp-server
```

### Performance Metrics
- CPU usage per quality level
- Memory consumption
- Network bandwidth
- Stream stability metrics

## 🛠️ Configuration

### Environment Variables
```bash
# Live2 specific settings
LIVE2_RTMP_PORT=1935
LIVE2_HTTP_PORT=8080
LIVE2_CONTAINER_NAME=rtmp-hls-server

# Updated streaming URLs
RTMP_BASE_URL=rtmp://localhost:1935/live
HLS_BASE_URL=http://localhost:8080/hls
```

### Docker Compose Configuration
The system runs two containers:
- `vyoo-server`: Your main application (port 3000)
- `rtmp-server`: Live2 streaming container (ports 1935, 8080)

### Custom nginx.conf
Live2 uses an optimized nginx configuration with:
- Multiple worker processes for better performance
- Optimized buffer sizes for streaming
- CORS headers for web player compatibility
- Segment cleanup for storage management

## 🎮 Testing

### Stream Testing
```bash
# Test with different qualities
ffmpeg -i input.mp4 -c:v libx264 -preset ultrafast \
  -f flv rtmp://localhost:1935/live/my_stream

# Test with webcam
ffmpeg -f v4l2 -i /dev/video0 -c:v libx264 -preset ultrafast \
  -f flv rtmp://localhost:1935/live/webcam_stream
```

### Player Testing
Use the included HTML players in `live2/players/`:
- `hls.html`: Basic HLS player
- `hls_hlsjs.html`: Advanced HLS.js player
- `dash.html`: DASH player (if enabled)

## 🐛 Troubleshooting

### Common Issues

#### Container Not Starting
```bash
# Check logs
docker-compose logs rtmp-server

# Verify ports are free
sudo netstat -tlnp | grep -E ':(1935|8080)'
```

#### Stream Not Appearing
```bash
# Check if stream is being received
curl http://localhost:8080/stat

# Monitor live2 logs
cd live2 && ./monitor.sh
```

#### High CPU Usage
- Reduce quality levels in nginx.conf
- Lower bitrates for encoding
- Use faster encoding presets

### Performance Tuning

#### For Higher Concurrency
```nginx
# In live2/conf/nginx.conf
worker_processes auto;
worker_connections 2048;
```

#### For Lower Latency
```nginx
# Reduce fragment size (increases CPU usage)
hls_fragment 1s;
hls_playlist_length 3;
```

## 📈 Scaling

### Horizontal Scaling
- Run multiple Live2 instances on different ports
- Use load balancer to distribute streams
- Configure CDN for HLS delivery

### Vertical Scaling
- Increase container resources in docker-compose.yml
- Optimize ffmpeg encoding settings
- Use hardware acceleration if available

## 🔒 Security

### RTMP Security
- Stream key authentication
- IP-based access control
- Webhook validation for publish events

### HLS Security
- Token-based access control
- CORS configuration
- Rate limiting for HLS requests

## 📚 References

- [Live2 Container Documentation](./live2/README.md)
- [Nginx RTMP Module](https://github.com/arut/nginx-rtmp-module)
- [HLS Specification](https://tools.ietf.org/html/rfc8216)
- [FFmpeg Streaming Guide](https://ffmpeg.org/ffmpeg-formats.html#hls-2)

---

**🎉 You now have a production-ready streaming infrastructure integrated with your Vyoo server!** 