# LLHLS (Low Latency HLS) Integration

This document describes the LLHLS integration implemented for the Vyoo streaming platform, which enables low-latency streaming using OvenMediaEngine instead of traditional NGINX RTMP.

## Overview

**LLHLS (Low Latency HLS)** reduces streaming latency from 12-30 seconds (regular HLS) to 5-10 seconds by using:
- Partial segments (500ms-2s) alongside full segments (6s)
- Blocking playlist reload mechanism
- HTTP/2 optimization
- Real-time webhook notifications

## Architecture Changes

### Before (NGINX RTMP)
```
RTMP Stream → NGINX RTMP → on_publish/on_publish_done → /live/start & /live/end
```

### After (LLHLS with OvenMediaEngine)
```
RTMP Stream → OvenMediaEngine → AdmissionWebhook → /live (single endpoint)
```

## New API Endpoints

### LLHLS Webhook Endpoint
- **URL**: `POST /v1/api/stream/live`
- **Purpose**: Handles OvenMediaEngine AdmissionWebhook calls
- **Authentication**: Secret key validation (`LLHLS_WEBHOOK_SECRET`)

### Enhanced Stream URLs Endpoint
- **URL**: `GET /v1/api/stream/urls/:streamKey`
- **Returns**: Both HLS and LLHLS URLs for client flexibility

## Configuration

### Environment Variables
```bash
# LLHLS webhook secret (must match OvenMediaEngine config)
LLHLS_WEBHOOK_SECRET=ome-webhook-secret-2024

# Live2 container settings
LIVE2_RTMP_PORT=1935
LIVE2_HTTP_PORT=8080
```

### OvenMediaEngine Configuration
The `live2/ome_conf/Server.xml` file is configured with:
```xml
<AdmissionWebhooks>
    <ControlServerUrl>http://172.17.0.1:8081/v1/api/stream/live</ControlServerUrl>
    <SecretKey>ome-webhook-secret-2024</SecretKey>
    <Timeout>5000</Timeout>
    <Enables>
        <Providers>rtmp</Providers>
        <Publishers>rtmp</Publishers>
    </Enables>
</AdmissionWebhooks>
```

## Webhook Payload Format

### Stream Start (status: "opening")
```json
{
  "request": {
    "application": "app",
    "stream": {
      "name": "userId_streamId"
    },
    "status": "opening"
  }
}
```

### Stream End (status: "closing")
```json
{
  "request": {
    "application": "app",
    "stream": {
      "name": "userId_streamId"
    },
    "status": "closing"
  }
}
```

## URL Formats

### LLHLS URLs
- **Master Playlist**: `http://localhost:8080/app/{streamKey}/llhls.m3u8`
- **Quality Specific**: `http://localhost:8080/app/{streamKey}_{quality}/llhls.m3u8`

### Legacy HLS URLs (still supported)
- **Master Playlist**: `http://localhost:8080/hls/{streamKey}.m3u8`
- **Quality Specific**: `http://localhost:8080/hls/{streamKey}_{quality}.m3u8`

## Backward Compatibility

The integration maintains backward compatibility:
- Legacy `/live/start` and `/live/end` endpoints remain functional
- Existing HLS URLs continue to work
- NGINX RTMP configurations are still supported

## Testing

Use the provided test script to validate the integration:

```bash
# Test stream start
node test-llhls-webhook.js start user123_stream456

# Test stream end
node test-llhls-webhook.js end user123_stream456
```

## Key Benefits

1. **Reduced Latency**: 5-10 seconds vs 12-30 seconds
2. **Single Endpoint**: Simplified webhook handling
3. **Better Performance**: HTTP/2 optimization
4. **Real-time Updates**: Immediate stream status changes
5. **Dual Support**: Both HLS and LLHLS URLs available

## Implementation Details

### New Files/Changes
- `src/middlewares/restrict-ip.ts` - Added `authenticateLLHLSWebhook`
- `src/controllers/streamControllers/liveStream.controller.ts` - Added `handleLLHLSWebhook`
- `src/routes/stream.router.ts` - Added `/live` endpoint
- `src/services/streamService/live2.service.ts` - Added LLHLS URL methods
- `src/config/environment.ts` - Added `LLHLS_WEBHOOK_SECRET`

### Security
- Secret key validation for webhook authentication
- Request payload validation
- Proper error handling and logging

## Monitoring

The integration includes comprehensive logging:
- Webhook reception and processing
- Stream lifecycle events
- Error conditions and debugging information

All logs use the structured logger with appropriate log levels for production monitoring.
