# =============================================================
# VYOO STREAMING SERVER: Quick Start Commands for Testing
# =============================================================
#
# Follow these steps to test the complete streaming workflow on your server.
#
# PREQUISITES:
# - The main server is running (e.g., on port 8000).
# - The Live2 RTMP/HLS server is running (e.g., on port 8080).
# - `jq` and `curl` are installed on your system.
#
# Replace `http://your-server-ip:8000` with your actual server address.

# -------------------------------------------------------------
# STEP 1: Register a New User
# -------------------------------------------------------------
# This command creates a new user. Change the email and password as you like.

echo "STEP 1: Registering a new user..."
curl -X POST -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "testuser",
    "displayName": "Test User"
  }' \
  http://your-server-ip:8000/v1/api/auth/register
echo


# -------------------------------------------------------------
# STEP 2: Log In and Get JWT Token
# -------------------------------------------------------------
# This command logs in the user and extracts the JWT token.
# The token is saved to a variable and a file for later use.

echo "STEP 2: Logging in and getting JWT token..."
export TOKEN=$(curl -s -X POST -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }' \
  http://your-server-ip:8000/v1/api/auth/login | jq -r '.token')

echo $TOKEN > .token
echo "✅ Token saved to environment variable \$TOKEN and to a file named .token"
echo


# -------------------------------------------------------------
# STEP 3: Create a Stream and Get Stream Key
# -------------------------------------------------------------
# This command uses the JWT token to create a new stream.
# It then extracts the stream key for you to use in your broadcaster.

echo "STEP 3: Creating a new stream..."
export STREAM_KEY=$(curl -s -X POST -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "My Live Test Stream",
    "description": "Testing the full workflow!"
  }' \
  http://your-server-ip:8000/v1/api/stream | jq -r '.stream.streamKey')

echo "✅ Stream created successfully!"
echo "   Your Stream Key is: $STREAM_KEY"
echo


# -------------------------------------------------------------
# STEP 4: Start Streaming with FFmpeg or OBS
# -------------------------------------------------------------
# Use the RTMP URL and the Stream Key from above in your broadcasting software.

echo "STEP 4: Start your stream now."
echo "--------------------------------"
echo "RTMP URL: rtmp://your-server-ip:1935/live"
echo "Stream Key: $STREAM_KEY"
echo "--------------------------------"
echo
echo "Example FFmpeg command:"
echo "ffmpeg -re -f lavfi -i testsrc2=size=1280x720:rate=30 -c:v libx264 -preset ultrafast -f flv \"rtmp://your-server-ip:1935/live/$STREAM_KEY\""
echo


# -------------------------------------------------------------
# STEP 5: Check the Stream Status
# -------------------------------------------------------------
# Once you've started streaming, use these URLs to check the status.

echo "STEP 5: Check your live stream."
echo "--------------------------------"
echo "HLS Playback URL (for players like VLC):"
echo "http://your-server-ip:8080/hls/$STREAM_KEY.m3u8"
echo
echo "Streaming Server Statistics (to see active streams):"
echo "http://your-server-ip:8080/stat"
echo
echo "============================================================="
echo "✅ Testing workflow complete."
echo "=============================================================" 