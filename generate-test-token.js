#!/usr/bin/env node

/**
 * Generate a test JWT token for LLHLS webhook testing
 * 
 * This script creates a JWT token that matches the format expected by
 * the updateLiveStreamStatus function.
 */

const jwt = require('jsonwebtoken');

const JWT_SECRET = 'secret'; // From .env file

function generateTestToken(userId, liveStreamId) {
  const payload = {
    userId: userId,
    liveStreamId: liveStreamId
  };
  
  const token = jwt.sign(payload, JWT_SECRET);
  return token;
}

// Generate test token with valid MongoDB ObjectId
const userId = '507f1f77bcf86cd799439011'; // Valid MongoDB ObjectId format
const liveStreamId = 'stream456';
const token = generateTestToken(userId, liveStreamId);

console.log('🔑 Generated Test JWT Token:');
console.log('Token:', token);
console.log('');
console.log('📋 Token Details:');
console.log('User ID:', userId);
console.log('Live Stream ID:', liveStreamId);
console.log('');
console.log('🧪 Use this token for testing:');
console.log(`node test-llhls-webhook.js start ${token}`);
console.log(`node test-llhls-webhook.js end ${token}`);
