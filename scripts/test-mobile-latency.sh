#!/bin/bash

# Mobile-Specific Latency Testing Script for Vyoo Streaming Platform
# Tests and validates latency optimizations for mobile-only deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_SERVER="http://localhost:8081"
STREAMING_SERVER="http://localhost:8080"
TEST_STREAM_KEY="mobile_test_$(date +%s)"
RTMP_SERVER="rtmp://localhost:1935/live"
LOG_FILE="/tmp/mobile_latency_test_$(date +%Y%m%d_%H%M%S).log"

# Mobile-specific thresholds (stricter than general thresholds)
MOBILE_LATENCY_EXCELLENT=1500    # 1.5s
MOBILE_LATENCY_GOOD=2500         # 2.5s
MOBILE_LATENCY_ACCEPTABLE=4000   # 4s
PLAYLIST_RESPONSE_TARGET=300     # 300ms
SEGMENT_ACCESS_TARGET=200        # 200ms

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Check if required tools are available
check_dependencies() {
    print_header "Checking Dependencies"
    
    local deps=("curl" "ffmpeg" "jq" "bc")
    for dep in "${deps[@]}"; do
        if command -v "$dep" >/dev/null 2>&1; then
            print_success "$dep is available"
        else
            print_error "$dep is required but not installed"
            exit 1
        fi
    done
}

# Check if services are running
check_services() {
    print_header "Checking Services"
    
    # Check API server
    if curl -s "$API_SERVER/health" >/dev/null; then
        print_success "API server is running"
    else
        print_error "API server is not accessible at $API_SERVER"
        exit 1
    fi
    
    # Check streaming server
    if curl -s "$STREAMING_SERVER/stat" >/dev/null; then
        print_success "Streaming server is running"
    else
        print_error "Streaming server is not accessible at $STREAMING_SERVER"
        exit 1
    fi
}

# Test LLHLS playlist response time
test_playlist_latency() {
    print_header "Testing LLHLS Playlist Latency"
    
    local stream_key="$1"
    local url="$STREAMING_SERVER/app/$stream_key/llhls.m3u8"
    
    log "Testing playlist access: $url"
    
    # Measure response time
    local response_time=$(curl -w "%{time_total}" -s -o /dev/null "$url" 2>/dev/null || echo "999")
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
    
    log "Playlist response time: ${response_time_ms}ms"
    
    if [ "$response_time_ms" -lt "$PLAYLIST_RESPONSE_TARGET" ]; then
        print_success "Playlist latency: ${response_time_ms}ms (Excellent for mobile)"
        return 0
    elif [ "$response_time_ms" -lt 500 ]; then
        print_warning "Playlist latency: ${response_time_ms}ms (Good for mobile)"
        return 1
    else
        print_error "Playlist latency: ${response_time_ms}ms (Too high for mobile)"
        return 2
    fi
}

# Test segment access latency
test_segment_latency() {
    print_header "Testing Segment Access Latency"
    
    local stream_key="$1"
    local url="$STREAMING_SERVER/app/$stream_key/"
    
    log "Testing segment access: $url"
    
    # Measure response time for segment directory
    local response_time=$(curl -w "%{time_total}" -s -o /dev/null -I "$url" 2>/dev/null || echo "999")
    local response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d. -f1)
    
    log "Segment access time: ${response_time_ms}ms"
    
    if [ "$response_time_ms" -lt "$SEGMENT_ACCESS_TARGET" ]; then
        print_success "Segment latency: ${response_time_ms}ms (Excellent for mobile)"
        return 0
    elif [ "$response_time_ms" -lt 400 ]; then
        print_warning "Segment latency: ${response_time_ms}ms (Good for mobile)"
        return 1
    else
        print_error "Segment latency: ${response_time_ms}ms (Too high for mobile)"
        return 2
    fi
}

# Start test stream
start_test_stream() {
    print_header "Starting Mobile Test Stream"
    
    log "Starting RTMP test stream with key: $TEST_STREAM_KEY"
    
    # Generate mobile-optimized test stream
    ffmpeg -f lavfi -i testsrc2=size=1920x1080:rate=30 \
           -f lavfi -i sine=frequency=1000:sample_rate=48000 \
           -c:v libx264 -preset ultrafast -tune zerolatency \
           -b:v 2000k -maxrate 2000k -bufsize 1000k \
           -g 30 -keyint_min 30 -sc_threshold 0 \
           -c:a aac -b:a 128k \
           -f flv "$RTMP_SERVER/$TEST_STREAM_KEY" \
           > /tmp/ffmpeg_mobile_test.log 2>&1 &
    
    local ffmpeg_pid=$!
    echo $ffmpeg_pid > /tmp/mobile_test_ffmpeg.pid
    
    print_info "Test stream started (PID: $ffmpeg_pid)"
    print_info "Waiting 10 seconds for stream to stabilize..."
    sleep 10
    
    # Verify stream is live
    if curl -s "$STREAMING_SERVER/app/$TEST_STREAM_KEY/llhls.m3u8" | grep -q "#EXTM3U"; then
        print_success "Test stream is live and generating LLHLS playlist"
        return 0
    else
        print_error "Test stream failed to start properly"
        return 1
    fi
}

# Stop test stream
stop_test_stream() {
    print_header "Stopping Test Stream"
    
    if [ -f /tmp/mobile_test_ffmpeg.pid ]; then
        local pid=$(cat /tmp/mobile_test_ffmpeg.pid)
        if kill "$pid" 2>/dev/null; then
            print_success "Test stream stopped"
        else
            print_warning "Test stream may have already stopped"
        fi
        rm -f /tmp/mobile_test_ffmpeg.pid
    fi
}

# Comprehensive mobile latency test
run_mobile_latency_test() {
    print_header "Running Comprehensive Mobile Latency Test"
    
    local stream_key="$TEST_STREAM_KEY"
    local total_score=0
    local max_score=6
    
    # Test playlist latency multiple times
    print_info "Testing playlist latency (5 iterations)..."
    local playlist_scores=0
    for i in {1..5}; do
        if test_playlist_latency "$stream_key"; then
            playlist_scores=$((playlist_scores + 2))
        elif [ $? -eq 1 ]; then
            playlist_scores=$((playlist_scores + 1))
        fi
        sleep 1
    done
    
    # Test segment latency multiple times
    print_info "Testing segment latency (5 iterations)..."
    local segment_scores=0
    for i in {1..5}; do
        if test_segment_latency "$stream_key"; then
            segment_scores=$((segment_scores + 2))
        elif [ $? -eq 1 ]; then
            segment_scores=$((segment_scores + 1))
        fi
        sleep 1
    done
    
    total_score=$((playlist_scores + segment_scores))
    local percentage=$((total_score * 100 / (max_score * 10)))
    
    print_header "Mobile Latency Test Results"
    log "Playlist performance: $playlist_scores/10"
    log "Segment performance: $segment_scores/10"
    log "Total score: $total_score/20 ($percentage%)"
    
    if [ "$percentage" -ge 90 ]; then
        print_success "EXCELLENT: Stream is optimally configured for mobile consumption"
    elif [ "$percentage" -ge 75 ]; then
        print_success "GOOD: Stream performs well for mobile devices"
    elif [ "$percentage" -ge 60 ]; then
        print_warning "FAIR: Stream needs optimization for better mobile performance"
    else
        print_error "POOR: Stream requires significant optimization for mobile use"
    fi
    
    return $percentage
}

# Test mobile-specific API endpoints
test_mobile_api_endpoints() {
    print_header "Testing Mobile-Specific API Endpoints"
    
    # Test mobile latency metrics endpoint
    local metrics_url="$API_SERVER/v1/api/stream/latency/metrics/$TEST_STREAM_KEY"
    if curl -s "$metrics_url" | jq . >/dev/null 2>&1; then
        print_success "Mobile latency metrics endpoint working"
    else
        print_warning "Mobile latency metrics endpoint not responding"
    fi
    
    # Test stream health endpoint
    local health_url="$API_SERVER/v1/api/stream/health"
    if curl -s "$health_url" | jq . >/dev/null 2>&1; then
        print_success "Stream health endpoint working"
    else
        print_warning "Stream health endpoint not responding"
    fi
    
    # Test stream status endpoint
    local status_url="$API_SERVER/v1/api/stream/is-live/$TEST_STREAM_KEY"
    if curl -s "$status_url" | jq . >/dev/null 2>&1; then
        print_success "Stream status endpoint working"
    else
        print_warning "Stream status endpoint not responding"
    fi
}

# Generate mobile optimization report
generate_report() {
    print_header "Generating Mobile Optimization Report"
    
    local report_file="/tmp/mobile_optimization_report_$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "test_timestamp": "$(date -Iseconds)",
  "test_stream_key": "$TEST_STREAM_KEY",
  "mobile_optimizations": {
    "llhls_enabled": true,
    "webrtc_fallback_removed": true,
    "cors_simplified": true,
    "segment_duration": "1s",
    "chunk_duration": "0.1s",
    "segment_count": 6,
    "browser_compatibility_removed": true
  },
  "performance_targets": {
    "playlist_response_target_ms": $PLAYLIST_RESPONSE_TARGET,
    "segment_access_target_ms": $SEGMENT_ACCESS_TARGET,
    "end_to_end_latency_target_ms": $MOBILE_LATENCY_EXCELLENT
  },
  "test_results": {
    "overall_score": "$1%",
    "log_file": "$LOG_FILE",
    "recommendations": [
      "Stream optimized for mobile-only deployment",
      "Browser-specific code removed",
      "LLHLS configuration optimized for mobile consumption",
      "Latency monitoring configured with mobile-specific thresholds"
    ]
  }
}
EOF
    
    print_success "Report generated: $report_file"
    print_info "Test log available: $LOG_FILE"
}

# Cleanup function
cleanup() {
    print_header "Cleaning Up"
    stop_test_stream
    print_info "Cleanup completed"
}

# Main execution
main() {
    print_header "Mobile Latency Optimization Test for Vyoo Streaming Platform"
    print_info "Testing mobile-specific optimizations and latency performance"
    print_info "Log file: $LOG_FILE"
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Run tests
    check_dependencies
    check_services
    
    if start_test_stream; then
        sleep 5  # Additional stabilization time
        
        local score
        if run_mobile_latency_test; then
            score=$?
        else
            score=0
        fi
        
        test_mobile_api_endpoints
        generate_report "$score"
        
        print_header "Test Summary"
        if [ "$score" -ge 75 ]; then
            print_success "Mobile optimization test PASSED with score: $score%"
            exit 0
        else
            print_warning "Mobile optimization test completed with score: $score%"
            print_info "Consider additional optimizations for better mobile performance"
            exit 1
        fi
    else
        print_error "Failed to start test stream"
        exit 1
    fi
}

# Run main function
main "$@"
