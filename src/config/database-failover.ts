import mongoose from 'mongoose';
import logger from './logger';

// Database failover configuration
interface DatabaseConfig {
  uri: string;
  options: mongoose.ConnectOptions;
  name: string;
  priority: number;
}

interface FailoverState {
  currentDatabase: string;
  connectionAttempts: number;
  lastFailureTime: number;
  isFailoverActive: boolean;
  healthCheckInterval?: NodeJS.Timeout;
}

class DatabaseFailover {
  private databases: DatabaseConfig[] = [];
  private state: FailoverState = {
    currentDatabase: '',
    connectionAttempts: 0,
    lastFailureTime: 0,
    isFailoverActive: false
  };
  
  private readonly maxRetries = 5;
  private readonly retryDelay = 5000; // 5 seconds
  private readonly failoverCooldown = 30000; // 30 seconds
  private readonly healthCheckInterval = 30000; // 30 seconds
  private readonly connectionTimeout = 10000; // 10 seconds

  constructor() {
    this.setupDatabases();
    this.setupEventHandlers();
  }

  private setupDatabases(): void {
    // Primary database
    this.databases.push({
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/vyoo',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: this.connectionTimeout,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        retryWrites: true,
        retryReads: true,
        readPreference: 'primary',
        writeConcern: {
          w: 'majority',
          j: true,
          wtimeout: 10000
        }
      },
      name: 'primary',
      priority: 1
    });

    // Fallback database (if configured)
    if (process.env.MONGODB_FALLBACK_URI) {
      this.databases.push({
        uri: process.env.MONGODB_FALLBACK_URI,
        options: {
          maxPoolSize: 5,
          serverSelectionTimeoutMS: this.connectionTimeout,
          socketTimeoutMS: 45000,
          bufferMaxEntries: 0,
          retryWrites: true,
          retryReads: true,
          readPreference: 'secondaryPreferred',
          writeConcern: {
            w: 'majority',
            j: true,
            wtimeout: 10000
          }
        },
        name: 'fallback',
        priority: 2
      });
    }

    // Sort databases by priority
    this.databases.sort((a, b) => a.priority - b.priority);
  }

  private setupEventHandlers(): void {
    // Handle connection events
    mongoose.connection.on('connected', () => {
      logger.info('Database connected successfully', { 
        database: this.state.currentDatabase,
        uri: this.getCurrentDatabaseUri()
      });
      this.state.connectionAttempts = 0;
      this.state.isFailoverActive = false;
      this.startHealthCheck();
    });

    mongoose.connection.on('error', (error) => {
      logger.error('Database connection error', { 
        error: error.message,
        database: this.state.currentDatabase,
        stack: error.stack
      });
      this.handleConnectionError(error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('Database disconnected', { 
        database: this.state.currentDatabase 
      });
      this.stopHealthCheck();
      this.handleDisconnection();
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('Database reconnected', { 
        database: this.state.currentDatabase 
      });
      this.state.connectionAttempts = 0;
    });

    // Handle process termination
    process.on('SIGINT', () => {
      this.gracefulShutdown();
    });

    process.on('SIGTERM', () => {
      this.gracefulShutdown();
    });
  }

  private getCurrentDatabaseUri(): string {
    const current = this.databases.find(db => db.name === this.state.currentDatabase);
    return current ? current.uri.replace(/\/\/.*@/, '//***:***@') : 'unknown';
  }

  private async handleConnectionError(error: Error): Promise<void> {
    this.state.connectionAttempts++;
    this.state.lastFailureTime = Date.now();

    logger.error('Database connection failed', {
      database: this.state.currentDatabase,
      attempt: this.state.connectionAttempts,
      maxRetries: this.maxRetries,
      error: error.message
    });

    if (this.state.connectionAttempts >= this.maxRetries) {
      logger.error('Max connection attempts reached, initiating failover', {
        database: this.state.currentDatabase,
        attempts: this.state.connectionAttempts
      });
      
      await this.initiateFailover();
    } else {
      // Retry with exponential backoff
      const delay = this.retryDelay * Math.pow(2, this.state.connectionAttempts - 1);
      logger.info(`Retrying database connection in ${delay}ms`, {
        database: this.state.currentDatabase,
        attempt: this.state.connectionAttempts
      });
      
      setTimeout(() => {
        this.reconnect();
      }, delay);
    }
  }

  private async handleDisconnection(): Promise<void> {
    if (!this.state.isFailoverActive) {
      logger.info('Attempting to reconnect to database', {
        database: this.state.currentDatabase
      });
      
      setTimeout(() => {
        this.reconnect();
      }, this.retryDelay);
    }
  }

  private async initiateFailover(): Promise<void> {
    if (this.state.isFailoverActive) {
      logger.warn('Failover already in progress');
      return;
    }

    this.state.isFailoverActive = true;
    logger.info('Starting database failover process');

    // Find next available database
    const currentIndex = this.databases.findIndex(db => db.name === this.state.currentDatabase);
    const nextDatabases = this.databases.slice(currentIndex + 1);

    for (const database of nextDatabases) {
      logger.info(`Attempting failover to database: ${database.name}`);
      
      try {
        await this.connectToDatabase(database);
        logger.info(`Failover successful to database: ${database.name}`);
        return;
      } catch (error) {
        logger.error(`Failover failed for database: ${database.name}`, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // If all failovers failed, try primary again after cooldown
    logger.error('All database failovers failed, will retry primary after cooldown');
    setTimeout(() => {
      this.state.isFailoverActive = false;
      this.state.connectionAttempts = 0;
      this.connectToDatabase(this.databases[0]);
    }, this.failoverCooldown);
  }

  private async connectToDatabase(database: DatabaseConfig): Promise<void> {
    try {
      // Close existing connection
      if (mongoose.connection.readyState !== 0) {
        await mongoose.connection.close();
      }

      // Connect to new database
      await mongoose.connect(database.uri, database.options);
      
      this.state.currentDatabase = database.name;
      this.state.connectionAttempts = 0;
      this.state.isFailoverActive = false;

      logger.info(`Successfully connected to database: ${database.name}`);
      
      // Perform health check
      await this.performHealthCheck();
      
    } catch (error) {
      logger.error(`Failed to connect to database: ${database.name}`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async reconnect(): Promise<void> {
    const currentDb = this.databases.find(db => db.name === this.state.currentDatabase);
    if (currentDb) {
      try {
        await this.connectToDatabase(currentDb);
      } catch (error) {
        logger.error('Reconnection failed', {
          database: this.state.currentDatabase,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  private async performHealthCheck(): Promise<boolean> {
    try {
      // Simple ping to check connection
      await mongoose.connection.db.admin().ping();
      
      // Check if we can perform basic operations
      const testCollection = mongoose.connection.db.collection('health_check');
      const testDoc = { timestamp: new Date(), check: 'connection_test' };
      
      await testCollection.insertOne(testDoc);
      await testCollection.deleteOne({ _id: testDoc._id });
      
      logger.debug('Database health check passed', {
        database: this.state.currentDatabase
      });
      
      return true;
    } catch (error) {
      logger.error('Database health check failed', {
        database: this.state.currentDatabase,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  private startHealthCheck(): void {
    this.stopHealthCheck(); // Clear any existing interval
    
    this.state.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.performHealthCheck();
      
      if (!isHealthy) {
        logger.warn('Database health check failed, connection may be unstable', {
          database: this.state.currentDatabase
        });
        
        // If health check fails multiple times, consider failover
        this.state.connectionAttempts++;
        if (this.state.connectionAttempts >= 3) {
          logger.error('Multiple health check failures, initiating failover');
          await this.initiateFailover();
        }
      } else {
        // Reset connection attempts on successful health check
        this.state.connectionAttempts = 0;
      }
    }, this.healthCheckInterval);
  }

  private stopHealthCheck(): void {
    if (this.state.healthCheckInterval) {
      clearInterval(this.state.healthCheckInterval);
      this.state.healthCheckInterval = undefined;
    }
  }

  private async gracefulShutdown(): Promise<void> {
    logger.info('Gracefully shutting down database connections');
    
    this.stopHealthCheck();
    
    try {
      await mongoose.connection.close();
      logger.info('Database connections closed successfully');
    } catch (error) {
      logger.error('Error closing database connections', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
    
    process.exit(0);
  }

  // Public methods
  public async initialize(): Promise<void> {
    if (this.databases.length === 0) {
      throw new Error('No databases configured for failover');
    }

    logger.info('Initializing database failover system', {
      databases: this.databases.map(db => ({ name: db.name, priority: db.priority }))
    });

    // Try to connect to primary database first
    await this.connectToDatabase(this.databases[0]);
  }

  public getConnectionStatus(): {
    currentDatabase: string;
    connectionState: string;
    isFailoverActive: boolean;
    connectionAttempts: number;
    lastFailureTime: number;
  } {
    return {
      currentDatabase: this.state.currentDatabase,
      connectionState: this.getConnectionStateString(),
      isFailoverActive: this.state.isFailoverActive,
      connectionAttempts: this.state.connectionAttempts,
      lastFailureTime: this.state.lastFailureTime
    };
  }

  private getConnectionStateString(): string {
    switch (mongoose.connection.readyState) {
      case 0: return 'disconnected';
      case 1: return 'connected';
      case 2: return 'connecting';
      case 3: return 'disconnecting';
      default: return 'unknown';
    }
  }

  public async forceFailover(): Promise<void> {
    logger.info('Force failover requested');
    this.state.connectionAttempts = this.maxRetries;
    await this.initiateFailover();
  }

  public async testAllConnections(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};
    
    for (const database of this.databases) {
      try {
        const testConnection = await mongoose.createConnection(database.uri, {
          ...database.options,
          serverSelectionTimeoutMS: 5000
        });
        
        await testConnection.db.admin().ping();
        await testConnection.close();
        
        results[database.name] = true;
        logger.info(`Database ${database.name} is accessible`);
      } catch (error) {
        results[database.name] = false;
        logger.error(`Database ${database.name} is not accessible`, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return results;
  }
}

// Export singleton instance
export const databaseFailover = new DatabaseFailover();

// Export connection status checker middleware
export const checkDatabaseConnection = (req: any, res: any, next: any) => {
  if (mongoose.connection.readyState !== 1) {
    logger.warn('Database connection not ready for request', {
      path: req.path,
      method: req.method,
      connectionState: mongoose.connection.readyState
    });
    
    return res.status(503).json({
      success: false,
      message: 'Database temporarily unavailable',
      error: 'DATABASE_CONNECTION_ERROR'
    });
  }
  
  next();
};

export default databaseFailover; 