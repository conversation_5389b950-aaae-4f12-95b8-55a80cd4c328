import { resolutionToBandwidth } from "../constants/hls";

export const generateFFMPEGArgs = (
  videoBitrate: string,
  audioBitrate: string,
  resolution: string,
  segmentFilePath: string,
  playListFilePath: string
): string[] => [
  "-i",
  "pipe:0",
  "-c:a",
  "aac",
  "-c:v",
  "libx264",
  "-b:v",
  videoBitrate,
  "-b:a",
  audioBitrate,
  "-vf",
  `scale=${resolution}`,
  "-f",
  "hls",
  "-hls_time",
  "2", // Reduced from 10s to 2s for mobile optimization
  "-hls_playlist_type",
  "vod",
  "-hls_segment_filename",
  segmentFilePath,
  playListFilePath,
];

export const generateMasterPlaylistFile = (variantPlaylists: any[]): string => {
  let masterPlaylist: string = variantPlaylists
    .map((variantPlaylist: any) => {
      const { resolution, outputFileName } = variantPlaylist;
      const bandwidth = resolutionToBandwidth[resolution as string];
      return `#EXT-X-STREAM-INF:BANDWIDTH=${bandwidth},RESOLUTION=${resolution}\n${outputFileName}`;
    })
    .join("\n");
  masterPlaylist = `#EXTM3U\n` + masterPlaylist;
  return masterPlaylist;
};
