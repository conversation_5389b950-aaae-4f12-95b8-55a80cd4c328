import { Request, Response } from "express";
import { UserService } from "../../services";
import { STATUS_CODES } from "../../constants/statusCodes";
import {
  APPLE_AUTHENTICATION,
  AUTHENTICATION,
  INTEREST_MESSAGES,
  MESSAGES,
  OTP_MESSAGES,
  USER_MESSAGES,
} from "../../constants/responseMessage";
import { ErrorResponse, SuccessResponse } from "../../utils/helper";
import OTP from "../../models/otp/otp.schema";
import { generateOTP } from "../../models/otp/otp.model";
import { sendMail } from "../../lib/mailer";
import logger from "../../config/logger";
import UserInterest from "../../models/userInterest/userInterest.schema";
import { IUser } from "../../types/schema";

/**
 * Regular signup with email including OTP verification.
 * @param req
 * @param res
 * @returns
 */
export const httpSignupWithEmail = async (req: Request, res: Response) => {
  try {
    let user: IUser | null = null;
    let message = "";
    const { username, password, email } = req.body;
    const existingUser = await UserService.getUserByEmail(email);
    if (existingUser && existingUser.verified) {
      message = AUTHENTICATION.USER_EXISTS;
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        message,
        existingUser
      );
    } else if (existingUser && !existingUser.verified) {
      // if user exists but email is not verified, then continue to sending otp to verify email
      user = existingUser;
      message = "Account exists but email not verified. OTP sent to email";
    } else {
      let newUser: Partial<IUser> = {
        email,
        provider: {
          provider: "email",
        },
        onboardingSteps: {
          emailVerified: true,
          phoneVerified: false,
          interestsFilled: false,
          usernameSet: !!username,
        },
      };

      if (username) newUser.username = username;
      if (password) newUser.password = password;

      user = await UserService.createUser(newUser);
      message = AUTHENTICATION.CREATE_ACCOUNT;
    }

    const otp = generateOTP();
    await OTP.create({
      email,
      otp,
    });

    const emailContent = `Hello ${
      username ? username : ""
    },\n\nYour OTP for verifying your account is: ${otp}.\nIt will expire in 2 minutes.\n\nThank you!`;
    await sendMail(email, "Verify Your Account", emailContent);
    return SuccessResponse(res, STATUS_CODES.CREATED, true, message, user);
  } catch (error: any) {
    logger.error(`Error during signup: ${error.message}`);
    if (error.code === 11000) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        AUTHENTICATION.USER_EXISTS
      );
    }
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      AUTHENTICATION.SIGNUP_FAILED
    );
  }
};

/**
 * Following controller is for verifying OTP fwhile signup.
 */
export const httpVerifySignupOTP = async (req: Request, res: Response) => {
  try {
    const { email, otp } = req.body;

    const otpDoc = await OTP.findOne({ email, otp });
    if (!otpDoc) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        OTP_MESSAGES.INVALID
      );
    }

    const user = await UserService.getUserByEmail(email);
    if (!user) {
      return ErrorResponse(
        res,
        STATUS_CODES.NOT_FOUND,
        false,
        OTP_MESSAGES.USER_NOT_FOUND
      );
    }

    user.onboardingSteps.emailVerified = true;
    await user.save();

    await OTP.deleteOne({ _id: otpDoc._id });

    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      OTP_MESSAGES.VERIFIED,
      user
    );
  } catch (error) {
    console.error("Error verifying OTP:", error);

    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      OTP_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Controller to resend OTP for signup verification.
 * we get user email and check if user is already verified, if yes, we return an error.
 * we will delete the old OTP and generate a new one.
 * we will send the new OTP to the user's email.
 *
 * @param req
 * @param res
 * @returns
 */
export const httpResendSignupOTP = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;
    const user = await UserService.getUserByEmail(email);
    if (!user) {
      return ErrorResponse(
        res,
        STATUS_CODES.NOT_FOUND,
        false,
        OTP_MESSAGES.USER_NOT_FOUND
      );
    }
    if (user.verified) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        OTP_MESSAGES.USER_VERIFIED,
        null
      );
    }
    const otp = generateOTP();
    await OTP.deleteMany({ email });

    await OTP.create({ email, otp });

    const emailContent = `Your new OTP is: ${otp} (valid for 2 minutes).`;
    await sendMail(email, "Resend Verification OTP", emailContent);

    return SuccessResponse(
      res,
      STATUS_CODES.OK,
      true,
      OTP_MESSAGES.SUCCESS,
      null
    );
  } catch (error) {
    console.error("Error resending OTP:", error);
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      OTP_MESSAGES.RESEND_ERROR
    );
  }
};
