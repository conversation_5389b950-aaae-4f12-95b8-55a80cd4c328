import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { JWT_SECRET } from '../config/environment';
import Stream from '../models/stream/stream.schema';
import User from '../models/user/user.schema';
import logger from '../config/logger';

interface AuthCache {
  [key: string]: {
    isValid: boolean;
    userId: string;
    liveStreamId: string;
    timestamp: number;
    attempts: number;
  };
}

interface RateLimitEntry {
  attempts: number;
  lastAttempt: number;
  isBlocked: boolean;
}

interface RateLimitCache {
  [key: string]: RateLimitEntry;
}

class RTMPAuthResilience {
  private authCache: AuthCache = {};
  private rateLimitCache: RateLimitCache = {};
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes
  private readonly maxAttempts = 10;
  private readonly rateLimitWindow = 60 * 1000; // 1 minute
  private readonly blockDuration = 10 * 60 * 1000; // 10 minutes
  private readonly cleanupInterval = 10 * 60 * 1000; // 10 minutes

  constructor() {
    this.startCleanupTimer();
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.cleanupInterval);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    
    // Cleanup auth cache
    for (const key in this.authCache) {
      if (now - this.authCache[key].timestamp > this.cacheTimeout) {
        delete this.authCache[key];
      }
    }
    
    // Cleanup rate limit cache
    for (const key in this.rateLimitCache) {
      const entry = this.rateLimitCache[key];
      if (now - entry.lastAttempt > this.blockDuration) {
        delete this.rateLimitCache[key];
      }
    }
  }

  private getClientIdentifier(req: Request): string {
    // Use multiple identifiers for more robust rate limiting
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    return `${ip}:${userAgent}`.slice(0, 100); // Limit length
  }

  private isRateLimited(clientId: string): boolean {
    const now = Date.now();
    const entry = this.rateLimitCache[clientId];
    
    if (!entry) {
      this.rateLimitCache[clientId] = {
        attempts: 1,
        lastAttempt: now,
        isBlocked: false
      };
      return false;
    }
    
    // Reset counter if window has passed
    if (now - entry.lastAttempt > this.rateLimitWindow) {
      entry.attempts = 1;
      entry.lastAttempt = now;
      entry.isBlocked = false;
      return false;
    }
    
    // Check if blocked
    if (entry.isBlocked && now - entry.lastAttempt < this.blockDuration) {
      return true;
    }
    
    // Increment attempts
    entry.attempts++;
    entry.lastAttempt = now;
    
    // Block if too many attempts
    if (entry.attempts > this.maxAttempts) {
      entry.isBlocked = true;
      logger.warn('Rate limit exceeded for RTMP auth', {
        clientId,
        attempts: entry.attempts,
        blockedUntil: new Date(now + this.blockDuration)
      });
      return true;
    }
    
    return false;
  }

  private getCachedAuth(streamKey: string): AuthCache[string] | null {
    const cached = this.authCache[streamKey];
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > this.cacheTimeout) {
      delete this.authCache[streamKey];
      return null;
    }
    
    return cached;
  }

  private setCachedAuth(streamKey: string, data: Omit<AuthCache[string], 'timestamp'>): void {
    this.authCache[streamKey] = {
      ...data,
      timestamp: Date.now()
    };
  }

  private async verifyStreamKeyWithFallback(streamKey: string): Promise<{
    isValid: boolean;
    userId?: string;
    liveStreamId?: string;
    stream?: any;
    user?: any;
  }> {
    try {
      // First attempt: Parse userId_liveStreamId format
      if (streamKey.includes('_')) {
        const parts = streamKey.split('_');
        if (parts.length === 2) {
          const [userId, liveStreamId] = parts;

          // Verify stream exists and is valid
          const stream = await Stream.findOne({
            streamKey: streamKey,
            creator: userId,
            liveStreamId: liveStreamId,
            status: { $in: ['draft', 'pending', 'live'] }
          }).populate('creator', 'username verified');

          if (stream && stream.creator.verified) {
            logger.info('Stream authenticated via userId_liveStreamId format', {
              streamKey: streamKey.slice(0, 10) + '...',
              userId,
              liveStreamId
            });

            return {
              isValid: true,
              userId,
              liveStreamId,
              stream,
              user: stream.creator
            };
          }
        }
      }

      // Second attempt: JWT verification (for backward compatibility)
      try {
        const decoded = jwt.verify(streamKey, JWT_SECRET) as { userId: string; liveStreamId: string };
        const { userId, liveStreamId } = decoded;

        // Verify stream exists and is valid
        const stream = await Stream.findOne({
          liveStreamId: liveStreamId,
          creator: userId,
          status: { $in: ['draft', 'pending', 'live'] }
        }).populate('creator', 'username verified');

        if (!stream) {
          logger.warn('Stream not found for valid JWT', { userId, liveStreamId });
          return { isValid: false };
        }

        if (!stream.creator.verified) {
          logger.warn('Stream creator not verified', { userId, streamId: stream._id });
          return { isValid: false };
        }

        return {
          isValid: true,
          userId,
          liveStreamId,
          stream,
          user: stream.creator
        };

      } catch (jwtError) {
        logger.debug('JWT verification failed, trying fallback methods', {
          error: jwtError instanceof Error ? jwtError.message : 'Unknown error',
          streamKey: streamKey.slice(0, 10) + '...'
        });
        
        // Fallback: Direct stream key lookup (for legacy compatibility)
        const stream = await Stream.findOne({
          streamKey: streamKey,
          status: { $in: ['draft', 'pending', 'live'] }
        }).populate('creator', 'username verified');
        
        if (stream && stream.creator.verified) {
          logger.info('Stream authenticated via fallback method', {
            streamId: stream._id,
            userId: stream.creator._id
          });
          
          return {
            isValid: true,
            userId: stream.creator._id.toString(),
            liveStreamId: stream.liveStreamId,
            stream,
            user: stream.creator
          };
        }
      }
      
      return { isValid: false };
      
    } catch (error) {
      logger.error('Error in stream key verification', {
        error: error instanceof Error ? error.message : 'Unknown error',
        streamKey: streamKey.slice(0, 10) + '...'
      });
      return { isValid: false };
    }
  }

  // Main authentication middleware
  public authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { name: streamKey } = req.body;
      const clientId = this.getClientIdentifier(req);
      
      // Basic validation
      if (!streamKey) {
        logger.warn('RTMP auth failed: missing stream key', { clientId });
        res.status(403).send('FORBIDDEN');
        return;
      }
      
      // Rate limiting
      if (this.isRateLimited(clientId)) {
        logger.warn('RTMP auth rate limited', { clientId, streamKey: streamKey.slice(0, 10) + '...' });
        res.status(429).send('TOO_MANY_REQUESTS');
        return;
      }
      
      // Check cache first
      const cached = this.getCachedAuth(streamKey);
      if (cached) {
        if (cached.isValid) {
          logger.debug('RTMP auth successful (cached)', {
            streamKey: streamKey.slice(0, 10) + '...',
            userId: cached.userId,
            liveStreamId: cached.liveStreamId
          });
          
          // Add auth data to request
          (req as any).authData = {
            userId: cached.userId,
            liveStreamId: cached.liveStreamId,
            streamKey
          };
          
          res.status(200).send('OK');
          return;
        } else {
          // If cached as invalid and not too many attempts, allow retry
          if (cached.attempts < 3) {
            cached.attempts++;
          } else {
            logger.warn('RTMP auth failed: too many cached failures', {
              streamKey: streamKey.slice(0, 10) + '...',
              attempts: cached.attempts
            });
            res.status(403).send('FORBIDDEN');
            return;
          }
        }
      }
      
      // Perform authentication with fallback
      const authResult = await this.verifyStreamKeyWithFallback(streamKey);
      
      if (authResult.isValid && authResult.userId && authResult.liveStreamId) {
        // Cache successful auth
        this.setCachedAuth(streamKey, {
          isValid: true,
          userId: authResult.userId,
          liveStreamId: authResult.liveStreamId,
          attempts: 0
        });
        
        // Add auth data to request
        (req as any).authData = {
          userId: authResult.userId,
          liveStreamId: authResult.liveStreamId,
          streamKey,
          stream: authResult.stream,
          user: authResult.user
        };
        
        logger.info('RTMP auth successful', {
          streamKey: streamKey.slice(0, 10) + '...',
          userId: authResult.userId,
          liveStreamId: authResult.liveStreamId,
          username: authResult.user?.username
        });
        
        res.status(200).send('OK');
        return;
      } else {
        // Cache failed auth
        const existingCache = this.getCachedAuth(streamKey);
        this.setCachedAuth(streamKey, {
          isValid: false,
          userId: '',
          liveStreamId: '',
          attempts: existingCache ? existingCache.attempts + 1 : 1
        });
        
        logger.warn('RTMP auth failed: invalid stream key', {
          streamKey: streamKey.slice(0, 10) + '...',
          clientId
        });
        
        res.status(403).send('FORBIDDEN');
        return;
      }
      
    } catch (error) {
      logger.error('RTMP auth middleware error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      
      // In case of system error, still allow the request but log it
      // This prevents complete service disruption
      res.status(500).send('INTERNAL_ERROR');
      return;
    }
  };

  // Enhanced authentication for stream lifecycle
  public authenticateStreamLifecycle = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { name: streamKey } = req.body;
      
      if (!streamKey) {
        res.status(403).send('FORBIDDEN');
        return;
      }
      
      // For stream lifecycle events, we need more robust validation
      const authResult = await this.verifyStreamKeyWithFallback(streamKey);
      
      if (authResult.isValid && authResult.stream) {
        // Add comprehensive auth data to request
        (req as any).authData = {
          userId: authResult.userId,
          liveStreamId: authResult.liveStreamId,
          streamKey,
          stream: authResult.stream,
          user: authResult.user
        };
        
        logger.info('Stream lifecycle auth successful', {
          event: req.path,
          streamKey: streamKey.slice(0, 10) + '...',
          userId: authResult.userId,
          streamId: authResult.stream._id
        });
        
        next();
        return;
      }
      
      logger.warn('Stream lifecycle auth failed', {
        event: req.path,
        streamKey: streamKey.slice(0, 10) + '...'
      });
      
      res.status(403).send('FORBIDDEN');
      return;
      
    } catch (error) {
      logger.error('Stream lifecycle auth error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        event: req.path
      });
      
      res.status(500).send('INTERNAL_ERROR');
      return;
    }
  };

  // Status endpoint for monitoring
  public getStatus = (req: Request, res: Response): void => {
    const now = Date.now();
    const authCacheSize = Object.keys(this.authCache).length;
    const rateLimitCacheSize = Object.keys(this.rateLimitCache).length;
    
    // Count active entries
    let validAuthEntries = 0;
    let blockedClients = 0;
    
    for (const entry of Object.values(this.authCache)) {
      if (entry.isValid && now - entry.timestamp < this.cacheTimeout) {
        validAuthEntries++;
      }
    }
    
    for (const entry of Object.values(this.rateLimitCache)) {
      if (entry.isBlocked && now - entry.lastAttempt < this.blockDuration) {
        blockedClients++;
      }
    }
    
    res.json({
      status: 'healthy',
      cache: {
        authCacheSize,
        validAuthEntries,
        rateLimitCacheSize,
        blockedClients
      },
      config: {
        cacheTimeout: this.cacheTimeout,
        maxAttempts: this.maxAttempts,
        rateLimitWindow: this.rateLimitWindow,
        blockDuration: this.blockDuration
      }
    });
  };
}

// Export singleton instance
export const rtmpAuthResilience = new RTMPAuthResilience();

export default rtmpAuthResilience; 