import Stream from "./stream.schema";
import { IShare, IStream } from "../../types/schema";
import User from "../user/user.schema";
import WatchHistory from "../watchHistory/watchHistory.schema";
import { PipelineStage } from "mongoose";
import {
  AWS_REGION,
  HLS_BUCKET,
  JWT_SECRET,
  RTMP_BASE_URL,
  RTMP_LIVE_URL,
} from "../../config/environment";
import { ResultDB } from "../../utils/helper";
import { STATUS_CODES } from "../../constants/statusCodes";
import { STREAM_MESSAGES } from "../../constants/responseMessage";
import { getPublicUrlFromS3 } from "../../lib/s3";
import { getNotInterestedFilterStages } from "../../utils/pipelineStages";
import * as jwt from "jsonwebtoken";
const mongoose = require("mongoose");

export const createStream = async (
  streamData: Partial<IStream>
): Promise<IStream> => {
  const stream = new Stream(streamData);
  return stream.save();
};

export const updateStream = async (
  streamId: string,
  updateData: Partial<IStream>
): Promise<IStream | null> => {
  return Stream.findByIdAndUpdate(streamId, updateData, { new: true });
};

export const getStreamById = async (
  streamId: string
): Promise<IStream | null> => {
  return Stream.findById(streamId).populate("creator reactions comments");
};
const getWatchedStreamIds = async (userId: string) => {
  const history = await WatchHistory.find({ userId }).select("streamId");
  return history.map((entry) => entry.streamId);
};
export const incrementViews = async (
  streamId: string
): Promise<IStream | null> => {
  return Stream.findByIdAndUpdate(
    streamId,
    { $inc: { views: 1 } },
    { new: true }
  );
};

/**
 * Add or remove a like from a stream.
 * @param userId - ID of the user liking or unliking the stream.
 * @param streamId - ID of the stream to like/unlike.
 * @param action - Action to perform: "like" or "unlike". Default is "like".
 * @returns Updated stream with like count or null if not found.
 */
export const updateStreamLikes = async (
  userId: string,
  streamId: string,
  action: "like" | "unlike" = "like"
): Promise<{ success: boolean; likesCount: number } | null> => {
  console.log("userId: ", userId, "streamId: ", streamId, "action: ", action);
  const updateAction =
    action === "like"
      ? { $addToSet: { likes: userId } }
      : { $pull: { likes: userId } };

  const updatedStream = await Stream.findByIdAndUpdate(streamId, updateAction, {
    new: true,
  }).populate("likes", "-_id -__v -password");

  if (!updatedStream) {
    throw new Error("Stream not found");
  }

  return {
    success: true,
    likesCount: updatedStream.likes.length,
  };
};

/**
 * Following service function provide streams for the user with necessary items.
 * @param userId
 * @param param1
 * @returns
 */
export const getStreamsPostedByUser = async (
  userId: string,
  {
    status,
    type,
    page,
    limit,
    sort,
  }: { status?: string; type?: string; page?: any; limit?: any; sort?: string }
) => {
  try {
    const currentPage = parseInt(page, 10) || 1;
    const pageLimit = parseInt(limit, 10) || 10;
    const sortOptions: Record<string, any> = {
      newest: { createdAt: -1 },
      oldest: { createdAt: 1 },
      mostViewed: { viewsCount: -1 },
    };

    // Ensure valid positive integers
    if (currentPage < 1 || pageLimit < 1) {
      throw new Error(
        "Invalid pagination parameters: page and limit must be positive integers."
      );
    }

    const pipeline: any[] = [
      {
        $match: {
          creator: new mongoose.Types.ObjectId(userId),
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          videoLength: 1,
          transcodedUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          isLive: 1,
          vodStatus: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          cover: 1,
          createdAt: 1,
          commentCount: { $size: "$comments" },
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            displayName: "$creatorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
      ...(sort && sortOptions[sort] ? [{ $sort: sortOptions[sort] }] : []),
      {
        $skip: (currentPage - 1) * pageLimit,
      },
      {
        $limit: pageLimit,
      },
    ];

    const totalPipeline = [
      {
        $match: {
          creator: new mongoose.Types.ObjectId(userId),
          isDeleted: false,
        },
      },
      { $count: "total" },
    ];

    const [streams, total] = await Promise.all([
      Stream.aggregate(pipeline).exec(),
      Stream.aggregate(totalPipeline).exec(),
    ]);

    const totalRecords = total[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / pageLimit);

    return {
      streams,
      pagination: {
        totalRecords,
        totalPages,
        currentPage,
        limit: pageLimit,
      },
    };
  } catch (error) {
    console.error("Error executing aggregation pipeline:", error);
    throw error;
  }
};

/**
 * Following Function gets All streams for user.
 * @param userId
 * @param param1
 * @returns
 */
export const getAllStreamsForUser = async (
  userId: string,
  {
    status,
    type,
    page,
    limit,
  }: { status?: string; type?: string; page?: any; limit?: any }
) => {
  try {
    const currentPage = parseInt(page, 10) || 1;
    const pageLimit = parseInt(limit, 10) || 10;

    // Ensure valid positive integers
    if (currentPage < 1 || pageLimit < 1) {
      throw new Error(
        "Invalid pagination parameters: page and limit must be positive integers."
      );
    }

    const pipeline: any[] = [
      {
        $match: {
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
          ...(status && { status }),
          ...(type && { type }),
        },
      },
      ...getNotInterestedFilterStages(userId, "stream"),
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          videoLength: 1,
          transcodedUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          isLive: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          createdAt: 1,
          cover: 1,
          vodStatus: 1,
          commentCount: { $size: "$comments" },
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            displayName: "$creatorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
      { $sort: { createdAt: -1 } },
      {
        $skip: (currentPage - 1) * pageLimit,
      },
      {
        $limit: pageLimit,
      },
    ];

    const totalPipeline = [
      {
        $match: {
          isDeleted: false,
          $or: [{ isLive: true }, { vodStatus: "ready" }],
          ...(status && { status }),
          ...(type && { type }),
        },
      },
      ...getNotInterestedFilterStages(userId, "stream"),
      { $count: "total" },
    ];

    const [data, total] = await Promise.all([
      Stream.aggregate(pipeline).exec(),
      Stream.aggregate(totalPipeline).exec(),
    ]);

    const totalRecords = total[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / pageLimit);

    return {
      data,
      pagination: {
        totalRecords,
        totalPages,
        currentPage,
        limit: pageLimit,
      },
    };
  } catch (error) {
    console.error("Error executing aggregation pipeline:", error);
    throw error;
  }
};

export const getStreamDetailsForUser = async (
  userId: string,
  streamId: string
) => {
  try {
    const streamDetailPipeline = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(streamId),
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          videoLength: 1,
          transcodedUrl: 1,
          liveUrls: 1,
          vodUrls: 1,
          liveUrl: 1,
          url: 1,
          type: 1,
          status: 1,
          isLive: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          cover: 1,
          createdAt: 1,
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          isLiked: {
            $cond: {
              if: {
                $in: [
                  new mongoose.Types.ObjectId(userId),
                  { $ifNull: ["$likes", []] },
                ],
              },
              then: true,
              else: false,
            },
          },
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    new mongoose.Types.ObjectId(userId),
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
          comments: { $size: "$comments" },
        },
      },
    ];

    const result = await Stream.aggregate(streamDetailPipeline).exec();

    return result[0];
  } catch (error) {
    console.log("Error fetching stream details:", error);
  }
};

/**
 * Function for Content Based Filtering(CBF) component
 * - we will fetch user preferences eg: tags from liked streams.
 * - fetching liked streams and tags from history of user activity.
 * - finally fetching streams matching the user's preferences.
 */
export const fetchContentBasedStreams = async (
  userId: string,
  limit: number = 10
) => {
  try {
    const user = await User.findById(userId).populate("following").exec();
    if (!user) throw new Error("User not found");

    const likedStreams = await Stream.find({ likes: userId })
      .select("tags category")
      .exec();

    const likedTags = likedStreams.flatMap((stream) => stream.tags);
    const preferredCategories = likedStreams.map((stream) => stream.category);
    const recommendations = await Stream.find({
      $or: [
        { tags: { $in: likedTags } },
        { category: { $in: preferredCategories } },
        { creator: { $in: user.following } },
      ],
      isDeleted: false,
    })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();

    return recommendations;
  } catch (error) {
    console.error("Error in content-based recommendations:", error);
    return [];
  }
};

/**
 * Function for Collaborative Filtering(CF) component
 * - we fetch users with similar likes/views
 * - Then we will fetch streams liked by other similar users but not watched by current user.
 */
export const fetchCollaborativeRecommendations = async (
  userId: string,
  limit: number = 10
) => {
  try {
    const similarUsers = await User.find({
      _id: { $ne: userId },
      likedVideos: { $in: await User.findById(userId).select("likedVideos") },
    })
      .select("_id")
      .exec();

    const similarUserIds = similarUsers.map((user) => user._id);
    const watchedStreamIds = await getWatchedStreamIds(userId);
    const recommendations = await Stream.find({
      likes: { $in: similarUserIds },
      _id: { $nin: watchedStreamIds },
      isDeleted: false,
    })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();

    return recommendations;
  } catch (error) {
    console.error("Error in collaborative recommendations:", error);
    return [];
  }
};

/**
 * Following function to fetch ternding stream with respective trending scores
 *
 */
export const fetchTrendingStreams = async (limit: number = 10) => {
  try {
    const trendingStreams = await Stream.aggregate([
      {
        $lookup: {
          from: "trendingscores",
          localField: "_id",
          foreignField: "streamId",
          as: "trendingData",
        },
      },
      { $unwind: "$trendingData" },
      { $sort: { "trendingData.trendingScore": -1 } },
      { $limit: limit },
    ]).exec();

    return trendingStreams;
  } catch (error) {
    console.error("Error fetching trending streams:", error);
    return [];
  }
};

/**
 * Hybrid Following function combines all the components
 * CBF + CF + trending streams.
 */
export const fetchHybridRecommendations = async (
  userId: string,
  limit: number = 20
) => {
  try {
    const [contentBased, collaborative, trending] = await Promise.all([
      fetchContentBasedStreams(userId, Math.floor(limit * 0.4)),
      fetchCollaborativeRecommendations(userId, Math.floor(limit * 0.4)),
      fetchTrendingStreams(Math.floor(limit * 0.2)),
    ]);

    const combinedResults = [...contentBased, ...collaborative, ...trending];
    const uniqueResults = Array.from(
      new Map(combinedResults.map((stream) => [stream._id.toString(), stream]))
    ).values();

    return Array.from(uniqueResults).slice(0, limit);
  } catch (error) {
    console.error("Error in hybrid recommendations:", error);
    return [];
  }
};

/**
 * Personalized Recommendation with aggregation Pipeline
 */

export const fetchPersonalizedRecommendation = async (
  userId: string,
  limit = 20
) => {
  try {
    const userObjectId = new mongoose.Types.ObjectId(userId);
    const pipeline: PipelineStage[] = [
      // {
      //   $match: {
      //     isDeleted: false,
      //     $or: [
      //       { isLive: true }, // Include live streams
      //       { vodStatus: "ready" }, // Include only fully processed VODs
      //     ],
      //   },
      // },
      // 1. Fetch streams
      {
        $lookup: {
          from: "trendingscores",
          localField: "_id",
          foreignField: "streamId",
          as: "trendingData",
        },
      },
      {
        $unwind: {
          path: "$trendingData",
          preserveNullAndEmptyArrays: true,
        },
      },

      // 2. Match streams liked by user or similar users (Collaborative Filtering)
      {
        $lookup: {
          from: "users",
          localField: "likes",
          foreignField: "_id",
          as: "likedByUsers",
        },
      },
      {
        $addFields: {
          similarUserLikes: {
            $filter: {
              input: "$likedByUsers",
              as: "user",
              cond: {
                $in: [userObjectId, { $ifNull: ["$$user.following", []] }],
              },
            },
          },
        },
      },

      // 3. Match streams with categories/tags user prefers (Content-Based Filtering)
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creatorDetails",
        },
      },
      {
        $unwind: {
          path: "$creatorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          matchesUserPreferences: {
            $cond: {
              if: {
                $or: [
                  {
                    $in: [
                      "$category",
                      { $ifNull: ["$creatorDetails.following", []] },
                    ],
                  },
                  { $in: [userObjectId, { $ifNull: ["$likes", []] }] },
                  { $in: [userObjectId, { $ifNull: ["$views", []] }] },
                ],
              },
              then: true,
              else: false,
            },
          },
        },
      },

      // 4. Exclude streams marked as "not interested" by the user
      ...getNotInterestedFilterStages(userId, "stream"),

      // 5. Calculate weighted score
      {
        $addFields: {
          cbfScore: {
            $cond: {
              if: "$matchesUserPreferences",
              then: { $multiply: ["$trendingData.trendingScore", 0.4] },
              else: 0,
            },
          },
          cfScore: {
            $cond: {
              if: { $size: "$similarUserLikes" },
              then: { $multiply: ["$trendingData.trendingScore", 0.4] },
              else: 0,
            },
          },
          trendingScore: {
            $multiply: ["$trendingData.trendingScore", 0.2],
          },
          finalScore: {
            $sum: ["$cbfScore", "$cfScore", "$trendingScore"],
          },
        },
      },

      // 6. Filter out deleted streams or streams user has watched
      {
        $lookup: {
          from: "watchhistories",
          localField: "_id",
          foreignField: "streamId",
          as: "watchedData",
        },
      },
      {
        $match: {
          isDeleted: false,
          "watchedData.userId": { $ne: userObjectId },
        },
      },

      // 7. Sort by final score
      {
        $sort: { finalScore: -1 },
      },
      {
        $limit: limit,
      },

      {
        $project: {
          _id: 1,
          title: 1,
          description: 1,
          category: 1,
          tags: 1,
          url: 1,
          transcodedUrl: 1,
          liveUrl: 1,
          videoLength: 1,
          cover: 1,
          isLive: 1,
          isLiked: {
            $cond: {
              if: {
                $in: [userObjectId, { $ifNull: ["$likes", []] }],
              },
              then: true,
              else: false,
            },
          },
          viewsCount: { $size: "$views" },
          likesCount: { $size: "$likes" },
          finalScore: 1,
          creator: {
            _id: "$creatorDetails._id",
            username: "$creatorDetails.username",
            profilePicture: "$creatorDetails.profilePicture",
            bio: "$creatorDetails.bio",
            displayName: "$creatorDetails.displayName",
            isFollowing: {
              $cond: {
                if: {
                  $in: [
                    userObjectId,
                    { $ifNull: ["$creatorDetails.followers", []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
      },
    ];

    const personalizedRecommendations = await Stream.aggregate(pipeline).exec();

    return personalizedRecommendations;
  } catch (error) {
    console.error("Error fetching personalized recommendations:", error);
    throw new Error("Failed to fetch personalized recommendations");
  }
};

/**
 * Initate live streaming for user.
 * @param streamData
 * @param userId
 * @returns
 */
export const createLiveStreamChannel = async (
  streamData: any,
  userId: string
) => {
  const HLS_S3_BASE_URL = `https://${HLS_BUCKET}.s3.${AWS_REGION}.amazonaws.com`;
  try {
    const liveStreamId = new mongoose.Types.ObjectId().toString();

    // Create a JWT token as the stream key
    const streamKey = jwt.sign({ userId, liveStreamId }, JWT_SECRET, {
      expiresIn: "1d",
    });

    const key = `VOD/${userId}/${streamKey}.mp4`;
    const publicUrl = getPublicUrlFromS3(HLS_BUCKET, key);

    const liveUrl = `${RTMP_LIVE_URL}/${streamKey}.m3u8`;
    const liveUrl360p = `${RTMP_LIVE_URL}/${streamKey}_360p/index.m3u8`;
    const liveUrl480p = `${RTMP_LIVE_URL}/${streamKey}_480p/index.m3u8`;
    const liveUrl720p = `${RTMP_LIVE_URL}/${streamKey}_720p/index.m3u8`;
    const liveUrl1080p = `${RTMP_LIVE_URL}/${streamKey}_1080p/index.m3u8`;
    const liveUrl2K = `${RTMP_LIVE_URL}/${streamKey}_2k/index.m3u8`;

    const rtmpUrl = `${RTMP_BASE_URL}`;

    const newStream = new Stream({
      creator: userId,
      rtmpUrl: rtmpUrl,
      url: publicUrl,
      streamKey: streamKey,
      transcodedUrl: "",
      liveStreamId: liveStreamId,
      liveUrl: liveUrl,
      type: "video-live",
      status: "draft",
      isLive: false,

      ...streamData,
    });

    newStream.liveUrls.set("AUTO", liveUrl);
    newStream.liveUrls.set("360P", liveUrl360p);
    newStream.liveUrls.set("480P", liveUrl480p);
    newStream.liveUrls.set("720P", liveUrl720p);
    newStream.liveUrls.set("1080P", liveUrl1080p);
    newStream.liveUrls.set("2K", liveUrl2K);

    await newStream.save();
    const streamId = newStream._id;
    return ResultDB(200, true, "Stream found", {
      rtmpUrl,
      streamId,
      streamKey,
      liveUrl,
    });
  } catch (error) {
    console.error("Error creating live stream:", error);
    return ResultDB(500, true, "Internal server error", {});
  }
};

export const stopLiveStreamChannel = async (streamKey: string) => {
  try {
    const result = await Stream.findOneAndDelete({ streamKey });
    if (!result) {
      return ResultDB(STATUS_CODES.NOT_FOUND, false, STREAM_MESSAGES.NOT_FOUND);
    }
    return ResultDB(STATUS_CODES.OK, true, STREAM_MESSAGES.CHANNEL_STOPPED);
  } catch (error) {
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STREAM_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

export const updateLiveStreamStatus = async (
  streamKey: string,
  status: string,
  isLive: boolean,
  videoLength?: number | null
) => {
  try {
    const decoded = jwt.verify(streamKey, JWT_SECRET) as {
      userId: string;
      liveStreamId: string;
    };
    const { userId, liveStreamId } = decoded;
    return await Stream.findOneAndUpdate(
      { creator: userId, liveStreamId: liveStreamId },
      { status, isLive, videoLength },
      { new: true }
    );
  } catch (error) {
    console.error("Invalid stream key:", error);
    return null;
  }
};
