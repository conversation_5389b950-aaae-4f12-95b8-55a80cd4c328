// services/storyService.ts

import mongoose, { PipelineStage, Types } from "mongoose";
import { STORY_MESSAGES } from "../../constants/responseMessage";
import { STATUS_CODES } from "../../constants/statusCodes";
import StoryModel from "../../models/story/story.schema";
import User from "../../models/user/user.schema";
import { IDoodleOverlay, IStory, ITextOverlay } from "../../types/schema";
import { ApiResponse, ResultDB } from "../../utils/helper";
import { AWS_S3_TEMP_BUCKET_NAME } from "../../config/environment";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import s3Client from "../../config/s3";

export const createStory = async (
  userId: string,
  media: string[],
  caption?: string,
  location?: IStory["location"],
  textOverlays?: ITextOverlay[],
  doodleOverlays?: IDoodleOverlay[]
): Promise<ApiResponse<IStory>> => {
  try {
    const newStory = await StoryModel.create({
      user: userId,
      media,
      caption,
      location,
      textOverlays,
      doodleOverlays,
    });

    return ResultDB<IStory>(
      STATUS_CODES.CREATED,
      true,
      STORY_MESSAGES.CREATED,
      newStory
    );
  } catch (err) {
    console.error("Error creating story:", err);
    return ResultDB<IStory>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_CREATING,
      null
    );
  }
};

export const getUserStories = async (
  userId: string
): Promise<ApiResponse<IStory[]>> => {
  try {
    const userObjId = new Types.ObjectId(userId);

    const stories = await StoryModel.aggregate([
      {
        $match: {
          user: userObjId,
          expiresAt: { $gte: new Date() },
        },
      },
      {
        $addFields: {
          isViewed: {
            $in: [userObjId, "$viewBy"],
          },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: "$user" },
      {
        $project: {
          media: 1,
          caption: 1,
          viewBy: 1,
          location: 1,
          createdAt: 1,
          expiresAt: 1,
          isViewed: 1,
          "user._id": 1,
          "user.username": 1,
          "user.profilePicture": 1,
          textOverlays: 1,
          doodleOverlays: 1,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return ResultDB<IStory[]>(
      STATUS_CODES.OK,
      true,
      STORY_MESSAGES.FETCHED_USER_STORIES,
      stories
    );
  } catch (err) {
    console.error("Error in getUserStories:", err);
    return ResultDB<IStory[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING,
      null
    );
  }
};

export const getStoriesByFollowings = async (
  userId: string,
  page: number = 1,
  limit: number = 10
): Promise<ApiResponse<any>> => {
  try {
    const user = await User.findById(userId).select("following").lean();

    if (!user || !user.following?.length) {
      return ResultDB(
        STATUS_CODES.OK,
        true,
        STORY_MESSAGES.NO_STORIES_FOUND,
        []
      );
    }

    const skip = (page - 1) * limit;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          user: { $in: user.following },
          expiresAt: { $gte: new Date() },
        },
      },
      {
        $addFields: {
          isViewed: {
            $in: [new mongoose.Types.ObjectId(userId), "$viewBy"],
          },
        },
      },
      {
        $sort: {
          isViewed: 1, // false (unviewed) first
          createdAt: -1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: "$user" },
      {
        $project: {
          _id: 1,
          user: {
            _id: "$user._id",
            username: "$user.username",
            profilePicture: "$user.profilePicture",
          },
          media: 1,
          caption: 1,
          viewBy: 1,
          createdAt: 1,
          isViewed: 1,
          expiresAt: 1,
          textOverlays: 1,
          doodleOverlays: 1,
        },
      },
      { $skip: skip },
      { $limit: limit },
    ];

    const totalCountPipeline = [
      {
        $match: {
          user: { $in: user.following },
          expiresAt: { $gte: new Date() },
        },
      },
      { $count: "total" },
    ];

    const [stories, countResult] = await Promise.all([
      StoryModel.aggregate(pipeline),
      StoryModel.aggregate(totalCountPipeline),
    ]);

    const totalRecords = countResult[0]?.total || 0;
    const totalPages = Math.ceil(totalRecords / limit);

    return ResultDB(
      STATUS_CODES.OK,
      true,
      STORY_MESSAGES.FETCHED_FOLLOWINGS_STORIES,
      {
        stories,
        pagination: {
          totalRecords,
          totalPages,
          currentPage: page,
          limit,
        },
      }
    );
  } catch (error) {
    console.error("Error in getStoriesByFollowings:", error);
    return ResultDB(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING,
      null
    );
  }
};

export const markStoryAsViewed = async (
  storyId: string,
  viewerId: string
): Promise<ApiResponse<IStory>> => {
  try {
    const story = await StoryModel.findById(storyId);

    if (!story) {
      return ResultDB<IStory>(
        STATUS_CODES.NOT_FOUND,
        false,
        STORY_MESSAGES.NOT_FOUND,
        null
      );
    }

    const alreadyViewed = story.viewBy.some((id) => id.toString() === viewerId);

    if (alreadyViewed) {
      return ResultDB<IStory>(
        STATUS_CODES.OK,
        true,
        STORY_MESSAGES.ALREADY_VIEWED,
        story
      );
    }

    const updatedStory = await StoryModel.findByIdAndUpdate(
      storyId,
      { $addToSet: { viewBy: viewerId } }, // ensures uniqueness
      { new: true }
    );

    return ResultDB<IStory>(
      STATUS_CODES.OK,
      true,
      STORY_MESSAGES.VIEWED_SUCCESSFULLY,
      updatedStory
    );
  } catch (error) {
    console.error("Error in markStoryAsViewed:", error);
    return ResultDB<IStory>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_VIEWING,
      null
    );
  }
};

// services/storyService.ts
export const getUserStoriesById = async (
  userId: string
): Promise<ApiResponse<IStory[]>> => {
  try {
    const stories = await StoryModel.find({
      user: userId,
      expiresAt: { $gte: new Date() },
    })
      .sort({ createdAt: -1 })
      .populate("user", "_id username profilePicture");

    return ResultDB<IStory[]>(
      STATUS_CODES.OK,
      true,
      STORY_MESSAGES.FETCHED_USER_STORIES,
      stories
    );
  } catch (error) {
    console.error("Error in getUserStoriesById:", error);
    return ResultDB<IStory[]>(
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      STORY_MESSAGES.ERROR_FETCHING,
      null
    );
  }
};

export const generateStoryPreSignedUrl = async (
  userId: string,
  contentType: string // e.g., "image/jpeg" or "video/mp4"
): Promise<any> => {
  const extensionMap: Record<string, string> = {
    "video/mp4": "mp4",
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/webp": "webp",
    "image/heic": "heic",
  };

  const extension = extensionMap[contentType];
  if (!extension) {
    throw new Error(`Unsupported content type: ${contentType}`);
  }
  const key = `stories/${userId}/${Date.now()}.${extension}`;

  const command = new PutObjectCommand({
    Bucket: AWS_S3_TEMP_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  });
  let url = await getSignedUrl(s3Client, command, { expiresIn: 10 * 60 });

  return { url, key };
};
