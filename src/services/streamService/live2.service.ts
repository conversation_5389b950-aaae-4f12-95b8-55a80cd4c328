import axios from "axios";
import { LIVE2_HTTP_PORT, LIVE2_CONTAINER_NAME } from "../../config/environment";
import logger from "../../config/logger";
import { latencyMonitorService, LatencyMetrics } from './latencyMonitor.service';

interface StreamStats {
  name: string;
  isLive: boolean;
  viewers: number;
  bandwidth: {
    in: number;
    out: number;
  };
  duration: number;
}

interface Live2Stats {
  server: {
    uptime: number;
    connections: number;
  };
  streams: StreamStats[];
}

export class Live2Service {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `http://localhost:${LIVE2_HTTP_PORT}`;
  }

  /**
   * Get streaming statistics from live2 container
   */
  async getStreamingStats(): Promise<Live2Stats | null> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 5000,
        headers: {
          'Accept': 'application/xml'
        }
      });

      // Parse XML response to extract streaming data
      return this.parseStatsXML(response.data);
    } catch (error) {
      logger.error(`Failed to get streaming stats: ${error}`);
      return null;
    }
  }

  /**
   * Check if a specific stream is live
   */
  async isStreamLive(streamKey: string): Promise<boolean> {
    try {
      const stats = await this.getStreamingStats();
      if (!stats) return false;

      return stats.streams.some(stream => 
        stream.name === streamKey && stream.isLive
      );
    } catch (error) {
      logger.error(`Failed to check stream status: ${error}`);
      return false;
    }
  }

  /**
   * Get mobile-optimized streaming URL (LLHLS only for mobile apps)
   * This is the primary method for mobile-only deployment
   */
  getMobileStreamUrl(streamKey: string, quality?: string): string {
    const qualitySuffix = quality ? `_${quality}` : '';
    return `${this.baseUrl}/app/${streamKey}${qualitySuffix}/llhls.m3u8`;
  }

  /**
   * Get LLHLS playlist URL for a stream (Low Latency HLS)
   * @deprecated Use getMobileStreamUrl for mobile-only deployment
   */
  getLLHLSUrl(streamKey: string, quality?: string): string {
    return this.getMobileStreamUrl(streamKey, quality);
  }

  /**
   * Get LLHLS master playlist URL for adaptive streaming
   * This is the primary method for mobile adaptive streaming
   */
  getMobileAdaptiveStreamUrl(streamKey: string): string {
    return `${this.baseUrl}/app/${streamKey}/llhls.m3u8`;
  }

  /**
   * @deprecated Legacy HLS removed for mobile-only deployment
   */
  getHLSUrl(streamKey: string, quality?: string): string {
    // Redirect to mobile-optimized LLHLS
    return this.getMobileStreamUrl(streamKey, quality);
  }

  /**
   * @deprecated Legacy HLS removed for mobile-only deployment
   */
  getMasterPlaylistUrl(streamKey: string): string {
    // Redirect to mobile-optimized LLHLS
    return this.getMobileAdaptiveStreamUrl(streamKey);
  }

  /**
   * Get available quality levels for a stream
   */
  getAvailableQualities(): string[] {
    return ['4k', '2k', '1080'];
  }

  /**
   * Get mobile-optimized streaming configuration
   * Returns configuration optimized for mobile consumption
   */
  getMobileStreamingConfig() {
    return {
      segmentDuration: 1, // 1 second segments for mobile
      segmentCount: 6, // Reduced buffer for mobile
      chunkDuration: 0.1, // 100ms chunks
      partHoldBack: 0.3, // Minimal hold back
      targetLatency: 2000, // Target 2 seconds latency
      maxBufferSize: 6000, // 6 seconds max buffer
    };
  }

  /**
   * Get latest segments only for mobile optimization
   * Serves only the most recent segments to reduce buffering
   */
  async getLatestSegmentsUrl(streamKey: string, maxSegments: number = 3): Promise<string> {
    // For mobile apps, we can add a query parameter to request only latest segments
    return `${this.baseUrl}/app/${streamKey}/llhls.m3u8?latest=${maxSegments}`;
  }

  /**
   * Generate RTMP publish URL for streamers
   */
  generateRTMPUrl(streamKey: string): string {
    return `rtmp://localhost:1935/live/${streamKey}`;
  }

  /**
   * Health check for the streaming server
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 3000
      });
      return response.status === 200;
    } catch (error) {
      logger.error(`Live2 health check failed: ${error}`);
      return false;
    }
  }

  /**
   * Get current viewer count for a stream
   */
  async getViewerCount(streamKey: string): Promise<number> {
    try {
      const stats = await this.getStreamingStats();
      if (!stats) return 0;

      const stream = stats.streams.find(s => s.name === streamKey);
      return stream?.viewers || 0;
    } catch (error) {
      logger.error(`Failed to get viewer count: ${error}`);
      return 0;
    }
  }

  /**
   * Parse XML stats response from nginx-rtmp with comprehensive error handling
   */
  private parseStatsXML(xmlData: string): Live2Stats {
    const streams: StreamStats[] = [];
    
    try {
      if (!xmlData || typeof xmlData !== 'string') {
        logger.warn('Invalid XML data received for stats parsing');
        return this.getEmptyStats();
      }

      // Extract server uptime
      const uptimeMatch = xmlData.match(/<uptime>(\d+)<\/uptime>/);
      const uptime = uptimeMatch ? parseInt(uptimeMatch[1]) : 0;

      // Extract stream information from XML with better error handling
      const streamMatches = xmlData.match(/<stream>([\s\S]*?)<\/stream>/g);
      
      if (streamMatches) {
        streamMatches.forEach((streamXml, index) => {
          try {
            const name = this.extractXMLValue(streamXml, 'name') || `stream_${index}`;
            
            // Check multiple indicators for live status
            const hasPublishing = streamXml.includes('<publishing>');
            const hasActive = streamXml.includes('<active>');
            const hasClients = parseInt(this.extractXMLValue(streamXml, 'nclients') || '0') > 0;
            const isLive = hasPublishing || hasActive || hasClients;
            
            const viewers = Math.max(0, parseInt(this.extractXMLValue(streamXml, 'nclients') || '0'));
            const bwIn = Math.max(0, parseInt(this.extractXMLValue(streamXml, 'bw_in') || '0'));
            const bwOut = Math.max(0, parseInt(this.extractXMLValue(streamXml, 'bw_out') || '0'));
            
            // Try to extract time information
            const timeMatch = streamXml.match(/<time>(\d+)<\/time>/);
            const duration = timeMatch ? parseInt(timeMatch[1]) / 1000 : 0; // Convert ms to seconds
            
            streams.push({
              name,
              isLive,
              viewers,
              bandwidth: { in: bwIn, out: bwOut },
              duration
            });
            
            logger.debug(`Parsed stream: ${name}, live: ${isLive}, viewers: ${viewers}`);
          } catch (streamError) {
            logger.error(`Error parsing individual stream data: ${streamError}`);
            // Continue processing other streams
          }
        });
      }

      return {
        server: {
          uptime,
          connections: streams.length
        },
        streams
      };
    } catch (error) {
      logger.error(`Failed to parse XML stats: ${error}`);
      return this.getEmptyStats();
    }
  }

  /**
   * Return empty stats structure for error cases
   */
  private getEmptyStats(): Live2Stats {
    return {
      server: { uptime: 0, connections: 0 },
      streams: []
    };
  }

  /**
   * Extract value from XML tag
   */
  private extractXMLValue(xml: string, tag: string): string | null {
    const match = xml.match(new RegExp(`<${tag}>(.*?)<\/${tag}>`));
    return match ? match[1] : null;
  }

  /**
   * Get latency metrics for a specific stream
   */
  async getStreamLatencyMetrics(streamKey: string): Promise<LatencyMetrics | null> {
    return await latencyMonitorService.measureStreamLatency(streamKey);
  }

  /**
   * Get latency metrics for all active streams
   */
  async getAllLatencyMetrics(): Promise<LatencyMetrics[]> {
    return await latencyMonitorService.measureAllStreams();
  }

  /**
   * Get average latency for a stream
   */
  getAverageLatency(streamKey: string, windowSize: number = 10): Partial<LatencyMetrics> | null {
    return latencyMonitorService.getAverageLatency(streamKey, windowSize);
  }

  /**
   * Get latency history for a stream
   */
  getLatencyHistory(streamKey: string): LatencyMetrics[] {
    return latencyMonitorService.getMetricsHistory(streamKey);
  }

  /**
   * Start latency monitoring
   */
  startLatencyMonitoring(intervalMs: number = 5000): void {
    latencyMonitorService.startMonitoring(intervalMs);

    // Set up alert handlers
    latencyMonitorService.on('latencyAlert', (alert) => {
      logger.warn('Latency alert:', alert);
      // You can add additional alert handling here (e.g., notifications, auto-scaling)
    });
  }

  /**
   * Stop latency monitoring
   */
  stopLatencyMonitoring(): void {
    latencyMonitorService.stopMonitoring();
  }

  /**
   * Start monitoring a stream
   */
  async startStreamMonitoring(streamKey: string): Promise<void> {
    logger.info(`Starting monitoring for stream: ${streamKey}`);
    // Implementation for stream monitoring logic
  }

  /**
   * Stop monitoring a stream
   */
  async stopStreamMonitoring(streamKey: string): Promise<void> {
    logger.info(`Stopping monitoring for stream: ${streamKey}`);
    // Implementation for cleanup logic
  }
}

export const live2Service = new Live2Service(); 