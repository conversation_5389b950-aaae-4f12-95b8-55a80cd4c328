import axios from 'axios';
import logger from '../../config/logger';
import { EventEmitter } from 'events';

export interface NetworkCondition {
  bandwidth: number; // Mbps
  latency: number; // ms
  packetLoss: number; // percentage
  jitter: number; // ms
  quality: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
}

export interface AdaptiveSettings {
  chunkDuration: number;
  segmentDuration: number;
  segmentCount: number;
  maxBitrate: number;
  targetLatency: number;
}

export interface StreamRecoveryState {
  streamKey: string;
  lastSeen: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  backoffDelay: number;
  isRecovering: boolean;
}

export class NetworkResilienceService extends EventEmitter {
  private readonly baseUrl: string;
  private readonly recoveryStates: Map<string, StreamRecoveryState> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly defaultSettings: AdaptiveSettings;
  private readonly adaptiveSettings: Map<string, AdaptiveSettings> = new Map();

  constructor() {
    super();
    this.baseUrl = `http://localhost:8080`;
    this.defaultSettings = {
      chunkDuration: 0.1,
      segmentDuration: 1,
      segmentCount: 10,
      maxBitrate: 5000000, // 5 Mbps
      targetLatency: 2000, // 2 seconds
    };
  }

  /**
   * Start network monitoring and adaptive streaming
   */
  startNetworkMonitoring(intervalMs: number = 10000): void {
    if (this.monitoringInterval) {
      this.stopNetworkMonitoring();
    }

    logger.info('Starting network resilience monitoring', { intervalMs });
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.monitorNetworkConditions();
        await this.checkStreamHealth();
        await this.performAdaptiveAdjustments();
      } catch (error) {
        logger.error('Error during network monitoring:', error);
      }
    }, intervalMs);
  }

  /**
   * Stop network monitoring
   */
  stopNetworkMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Network resilience monitoring stopped');
    }
  }

  /**
   * Monitor current network conditions
   */
  async monitorNetworkConditions(): Promise<NetworkCondition> {
    try {
      const startTime = Date.now();
      
      // Measure latency with a simple ping to the streaming server
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 5000,
        headers: { 'Accept': 'application/xml' }
      });
      
      const latency = Date.now() - startTime;
      
      // Estimate bandwidth based on response time and size
      const responseSize = response.data.length;
      const estimatedBandwidth = this.estimateBandwidth(responseSize, latency);
      
      // Calculate network quality
      const condition: NetworkCondition = {
        bandwidth: estimatedBandwidth,
        latency,
        packetLoss: 0, // Would need more sophisticated measurement
        jitter: 0, // Would need multiple samples
        quality: this.calculateNetworkQuality(estimatedBandwidth, latency)
      };

      this.emit('networkCondition', condition);
      return condition;
    } catch (error) {
      logger.error('Failed to monitor network conditions:', error);
      return {
        bandwidth: 0,
        latency: 9999,
        packetLoss: 100,
        jitter: 0,
        quality: 'critical'
      };
    }
  }

  /**
   * Check health of all active streams
   */
  async checkStreamHealth(): Promise<void> {
    try {
      const activeStreams = await this.getActiveStreams();
      const currentTime = Date.now();

      // Check each active stream
      for (const streamKey of activeStreams) {
        const recoveryState = this.recoveryStates.get(streamKey);
        
        if (recoveryState) {
          recoveryState.lastSeen = currentTime;
          recoveryState.reconnectAttempts = 0; // Reset on successful check
        } else {
          // Initialize recovery state for new streams
          this.recoveryStates.set(streamKey, {
            streamKey,
            lastSeen: currentTime,
            reconnectAttempts: 0,
            maxReconnectAttempts: 5,
            backoffDelay: 1000,
            isRecovering: false
          });
        }
      }

      // Check for streams that have gone offline
      for (const [streamKey, state] of this.recoveryStates.entries()) {
        const timeSinceLastSeen = currentTime - state.lastSeen;
        
        if (timeSinceLastSeen > 30000 && !activeStreams.includes(streamKey)) {
          // Stream has been offline for more than 30 seconds
          await this.handleStreamDisconnection(streamKey, state);
        }
      }
    } catch (error) {
      logger.error('Failed to check stream health:', error);
    }
  }

  /**
   * Handle stream disconnection and recovery
   */
  async handleStreamDisconnection(streamKey: string, state: StreamRecoveryState): Promise<void> {
    if (state.isRecovering) {
      return; // Already attempting recovery
    }

    state.isRecovering = true;
    state.reconnectAttempts++;

    logger.warn(`Stream disconnected: ${streamKey}, attempt ${state.reconnectAttempts}/${state.maxReconnectAttempts}`);

    this.emit('streamDisconnected', {
      streamKey,
      attempt: state.reconnectAttempts,
      maxAttempts: state.maxReconnectAttempts
    });

    if (state.reconnectAttempts >= state.maxReconnectAttempts) {
      logger.error(`Stream recovery failed after ${state.maxReconnectAttempts} attempts: ${streamKey}`);
      this.emit('streamRecoveryFailed', { streamKey });
      this.recoveryStates.delete(streamKey);
      return;
    }

    // Exponential backoff
    const delay = state.backoffDelay * Math.pow(2, state.reconnectAttempts - 1);
    
    setTimeout(async () => {
      try {
        await this.attemptStreamRecovery(streamKey, state);
      } catch (error) {
        logger.error(`Stream recovery attempt failed: ${streamKey}`, error);
        state.isRecovering = false;
      }
    }, delay);
  }

  /**
   * Attempt to recover a disconnected stream
   */
  async attemptStreamRecovery(streamKey: string, state: StreamRecoveryState): Promise<void> {
    try {
      // Check if stream has naturally recovered
      const activeStreams = await this.getActiveStreams();
      
      if (activeStreams.includes(streamKey)) {
        logger.info(`Stream naturally recovered: ${streamKey}`);
        state.isRecovering = false;
        state.reconnectAttempts = 0;
        state.lastSeen = Date.now();
        this.emit('streamRecovered', { streamKey });
        return;
      }

      // Implement recovery logic here
      // This could involve:
      // 1. Notifying the streamer
      // 2. Attempting to restart the stream
      // 3. Switching to backup stream
      
      logger.info(`Attempting recovery for stream: ${streamKey}`);
      
      // For now, just mark as not recovering to allow next attempt
      state.isRecovering = false;
      
    } catch (error) {
      logger.error(`Recovery attempt failed for stream ${streamKey}:`, error);
      state.isRecovering = false;
    }
  }

  /**
   * Perform adaptive adjustments based on network conditions
   */
  async performAdaptiveAdjustments(): Promise<void> {
    try {
      const networkCondition = await this.monitorNetworkConditions();
      const activeStreams = await this.getActiveStreams();

      for (const streamKey of activeStreams) {
        const currentSettings = this.adaptiveSettings.get(streamKey) || { ...this.defaultSettings };
        const newSettings = this.calculateAdaptiveSettings(networkCondition, currentSettings);
        
        if (this.shouldUpdateSettings(currentSettings, newSettings)) {
          await this.applyAdaptiveSettings(streamKey, newSettings);
          this.adaptiveSettings.set(streamKey, newSettings);
          
          this.emit('adaptiveSettingsChanged', {
            streamKey,
            oldSettings: currentSettings,
            newSettings,
            networkCondition
          });
        }
      }
    } catch (error) {
      logger.error('Failed to perform adaptive adjustments:', error);
    }
  }

  /**
   * Calculate adaptive settings based on network conditions
   */
  private calculateAdaptiveSettings(condition: NetworkCondition, current: AdaptiveSettings): AdaptiveSettings {
    const settings = { ...current };

    switch (condition.quality) {
      case 'excellent':
        settings.chunkDuration = 0.1;
        settings.segmentDuration = 1;
        settings.segmentCount = 10;
        settings.targetLatency = 1500;
        break;
        
      case 'good':
        settings.chunkDuration = 0.15;
        settings.segmentDuration = 1.5;
        settings.segmentCount = 8;
        settings.targetLatency = 2000;
        break;
        
      case 'fair':
        settings.chunkDuration = 0.2;
        settings.segmentDuration = 2;
        settings.segmentCount = 6;
        settings.targetLatency = 3000;
        break;
        
      case 'poor':
        settings.chunkDuration = 0.3;
        settings.segmentDuration = 3;
        settings.segmentCount = 5;
        settings.targetLatency = 5000;
        break;
        
      case 'critical':
        settings.chunkDuration = 0.5;
        settings.segmentDuration = 4;
        settings.segmentCount = 4;
        settings.targetLatency = 8000;
        break;
    }

    return settings;
  }

  /**
   * Check if settings should be updated
   */
  private shouldUpdateSettings(current: AdaptiveSettings, proposed: AdaptiveSettings): boolean {
    const threshold = 0.1; // 10% change threshold
    
    return (
      Math.abs(current.chunkDuration - proposed.chunkDuration) > threshold ||
      Math.abs(current.segmentDuration - proposed.segmentDuration) > threshold ||
      current.segmentCount !== proposed.segmentCount
    );
  }

  /**
   * Apply adaptive settings (placeholder - would need OvenMediaEngine API)
   */
  private async applyAdaptiveSettings(streamKey: string, settings: AdaptiveSettings): Promise<void> {
    logger.info(`Applying adaptive settings for stream ${streamKey}:`, settings);
    
    // Note: OvenMediaEngine doesn't currently support runtime configuration changes
    // This would require either:
    // 1. OvenMediaEngine API integration
    // 2. Configuration file updates and restart
    // 3. Stream-specific configuration profiles
    
    // For now, just log the intended changes
    this.emit('settingsApplied', { streamKey, settings });
  }

  /**
   * Estimate bandwidth based on response time and size
   */
  private estimateBandwidth(responseSize: number, latency: number): number {
    if (latency === 0) return 0;
    
    // Simple estimation: bytes per second to Mbps
    const bytesPerSecond = (responseSize * 1000) / latency;
    const mbps = (bytesPerSecond * 8) / (1024 * 1024);
    
    return Math.max(0, mbps);
  }

  /**
   * Calculate network quality based on bandwidth and latency
   */
  private calculateNetworkQuality(bandwidth: number, latency: number): NetworkCondition['quality'] {
    if (latency > 1000 || bandwidth < 1) return 'critical';
    if (latency > 500 || bandwidth < 2) return 'poor';
    if (latency > 200 || bandwidth < 5) return 'fair';
    if (latency > 100 || bandwidth < 10) return 'good';
    return 'excellent';
  }

  /**
   * Get active streams from OvenMediaEngine
   */
  private async getActiveStreams(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 3000,
        headers: { 'Accept': 'application/xml' }
      });

      const streamMatches = response.data.match(/<stream>[\s\S]*?<\/stream>/g) || [];
      const activeStreams: string[] = [];

      for (const streamXml of streamMatches) {
        const nameMatch = streamXml.match(/<name>(.*?)<\/name>/);
        const isPublishing = streamXml.includes('<publishing>') || streamXml.includes('<active>');
        
        if (nameMatch && isPublishing) {
          activeStreams.push(nameMatch[1]);
        }
      }

      return activeStreams;
    } catch (error) {
      logger.error('Failed to get active streams:', error);
      return [];
    }
  }

  /**
   * Get current recovery states
   */
  getRecoveryStates(): Map<string, StreamRecoveryState> {
    return new Map(this.recoveryStates);
  }

  /**
   * Get adaptive settings for a stream
   */
  getAdaptiveSettings(streamKey: string): AdaptiveSettings | null {
    return this.adaptiveSettings.get(streamKey) || null;
  }

  /**
   * Force recovery attempt for a stream
   */
  async forceStreamRecovery(streamKey: string): Promise<void> {
    const state = this.recoveryStates.get(streamKey);
    if (state) {
      await this.attemptStreamRecovery(streamKey, state);
    }
  }
}

// Export singleton instance
export const networkResilienceService = new NetworkResilienceService();
