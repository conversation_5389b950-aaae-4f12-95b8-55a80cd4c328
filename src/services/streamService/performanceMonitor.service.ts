import axios from 'axios';
import logger from '../../config/logger';
import { EventEmitter } from 'events';
import { latencyMonitorService, LatencyMetrics } from './latencyMonitor.service';
import { networkResilienceService } from './networkResilience.service';

export interface PerformanceAlert {
  id: string;
  level: 'info' | 'warning' | 'critical';
  type: 'latency' | 'bandwidth' | 'cpu' | 'memory' | 'disk' | 'network' | 'stream';
  message: string;
  streamKey?: string;
  value: number;
  threshold: number;
  timestamp: number;
  resolved: boolean;
}

export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  timestamp: number;
}

export interface StreamPerformanceMetrics {
  streamKey: string;
  viewers: number;
  bitrate: number;
  fps: number;
  resolution: string;
  duration: number;
  droppedFrames: number;
  bufferHealth: number;
  latency: LatencyMetrics | null;
  timestamp: number;
}

export interface PerformanceThresholds {
  latency: {
    warning: number;
    critical: number;
  };
  cpu: {
    warning: number;
    critical: number;
  };
  memory: {
    warning: number;
    critical: number;
  };
  disk: {
    warning: number;
    critical: number;
  };
  viewers: {
    warning: number;
    critical: number;
  };
  bufferHealth: {
    warning: number;
    critical: number;
  };
}

export class PerformanceMonitorService extends EventEmitter {
  private readonly baseUrl: string;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly alerts: Map<string, PerformanceAlert> = new Map();
  private readonly metricsHistory: SystemMetrics[] = [];
  private readonly streamMetricsHistory: Map<string, StreamPerformanceMetrics[]> = new Map();
  private readonly maxHistorySize = 1000;
  
  private readonly thresholds: PerformanceThresholds = {
    latency: { warning: 2000, critical: 5000 },
    cpu: { warning: 70, critical: 90 },
    memory: { warning: 80, critical: 95 },
    disk: { warning: 85, critical: 95 },
    viewers: { warning: 1000, critical: 2000 },
    bufferHealth: { warning: 30, critical: 10 }
  };

  constructor() {
    super();
    this.baseUrl = `http://localhost:8080`;
    this.setupEventListeners();
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(intervalMs: number = 10000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    logger.info('Starting performance monitoring', { intervalMs });
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectSystemMetrics();
        await this.collectStreamMetrics();
        await this.checkThresholds();
        await this.cleanupOldAlerts();
      } catch (error) {
        logger.error('Error during performance monitoring:', error);
      }
    }, intervalMs);

    // Start related monitoring services
    latencyMonitorService.startMonitoring(5000);
    networkResilienceService.startNetworkMonitoring(10000);
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Performance monitoring stopped');
    }

    latencyMonitorService.stopMonitoring();
    networkResilienceService.stopNetworkMonitoring();
  }

  /**
   * Setup event listeners for other services
   */
  private setupEventListeners(): void {
    // Listen for latency alerts
    latencyMonitorService.on('latencyAlert', (alert) => {
      this.createAlert({
        level: alert.level as 'warning' | 'critical',
        type: 'latency',
        message: alert.message,
        streamKey: alert.streamKey,
        value: alert.value,
        threshold: alert.threshold
      });
    });

    // Listen for network resilience events
    networkResilienceService.on('streamDisconnected', (event) => {
      this.createAlert({
        level: 'warning',
        type: 'stream',
        message: `Stream disconnected: ${event.streamKey} (attempt ${event.attempt}/${event.maxAttempts})`,
        streamKey: event.streamKey,
        value: event.attempt,
        threshold: event.maxAttempts
      });
    });

    networkResilienceService.on('streamRecoveryFailed', (event) => {
      this.createAlert({
        level: 'critical',
        type: 'stream',
        message: `Stream recovery failed: ${event.streamKey}`,
        streamKey: event.streamKey,
        value: 0,
        threshold: 1
      });
    });
  }

  /**
   * Collect system metrics
   */
  async collectSystemMetrics(): Promise<SystemMetrics> {
    try {
      // Note: In a real implementation, you would collect actual system metrics
      // This is a simplified version for demonstration
      const metrics: SystemMetrics = {
        cpu: {
          usage: Math.random() * 100, // Would use actual CPU monitoring
          cores: 8
        },
        memory: {
          used: Math.random() * 8 * 1024 * 1024 * 1024, // Would use actual memory monitoring
          total: 8 * 1024 * 1024 * 1024,
          percentage: Math.random() * 100
        },
        disk: {
          used: Math.random() * 100 * 1024 * 1024 * 1024, // Would use actual disk monitoring
          total: 100 * 1024 * 1024 * 1024,
          percentage: Math.random() * 100
        },
        network: {
          bytesIn: Math.random() * 1000000,
          bytesOut: Math.random() * 1000000,
          packetsIn: Math.random() * 10000,
          packetsOut: Math.random() * 10000
        },
        timestamp: Date.now()
      };

      this.storeSystemMetrics(metrics);
      this.emit('systemMetrics', metrics);
      
      return metrics;
    } catch (error) {
      logger.error('Failed to collect system metrics:', error);
      throw error;
    }
  }

  /**
   * Collect stream performance metrics
   */
  async collectStreamMetrics(): Promise<StreamPerformanceMetrics[]> {
    try {
      const activeStreams = await this.getActiveStreams();
      const streamMetrics: StreamPerformanceMetrics[] = [];

      for (const streamKey of activeStreams) {
        try {
          const streamStats = await this.getStreamStats(streamKey);
          const latencyMetrics = await latencyMonitorService.measureStreamLatency(streamKey);
          
          const metrics: StreamPerformanceMetrics = {
            streamKey,
            viewers: streamStats?.viewers || 0,
            bitrate: streamStats?.bitrate || 0,
            fps: streamStats?.fps || 0,
            resolution: streamStats?.resolution || 'unknown',
            duration: streamStats?.duration || 0,
            droppedFrames: streamStats?.droppedFrames || 0,
            bufferHealth: latencyMetrics?.bufferHealth || 0,
            latency: latencyMetrics,
            timestamp: Date.now()
          };

          streamMetrics.push(metrics);
          this.storeStreamMetrics(metrics);
        } catch (error) {
          logger.error(`Failed to collect metrics for stream ${streamKey}:`, error);
        }
      }

      this.emit('streamMetrics', streamMetrics);
      return streamMetrics;
    } catch (error) {
      logger.error('Failed to collect stream metrics:', error);
      return [];
    }
  }

  /**
   * Check performance thresholds and create alerts
   */
  async checkThresholds(): Promise<void> {
    try {
      // Check system metrics
      const latestSystemMetrics = this.metricsHistory[this.metricsHistory.length - 1];
      if (latestSystemMetrics) {
        this.checkSystemThresholds(latestSystemMetrics);
      }

      // Check stream metrics
      for (const [streamKey, history] of this.streamMetricsHistory.entries()) {
        const latestMetrics = history[history.length - 1];
        if (latestMetrics) {
          this.checkStreamThresholds(latestMetrics);
        }
      }
    } catch (error) {
      logger.error('Failed to check thresholds:', error);
    }
  }

  /**
   * Check system performance thresholds
   */
  private checkSystemThresholds(metrics: SystemMetrics): void {
    // CPU usage check
    if (metrics.cpu.usage > this.thresholds.cpu.critical) {
      this.createAlert({
        level: 'critical',
        type: 'cpu',
        message: `Critical CPU usage: ${metrics.cpu.usage.toFixed(1)}%`,
        value: metrics.cpu.usage,
        threshold: this.thresholds.cpu.critical
      });
    } else if (metrics.cpu.usage > this.thresholds.cpu.warning) {
      this.createAlert({
        level: 'warning',
        type: 'cpu',
        message: `High CPU usage: ${metrics.cpu.usage.toFixed(1)}%`,
        value: metrics.cpu.usage,
        threshold: this.thresholds.cpu.warning
      });
    }

    // Memory usage check
    if (metrics.memory.percentage > this.thresholds.memory.critical) {
      this.createAlert({
        level: 'critical',
        type: 'memory',
        message: `Critical memory usage: ${metrics.memory.percentage.toFixed(1)}%`,
        value: metrics.memory.percentage,
        threshold: this.thresholds.memory.critical
      });
    } else if (metrics.memory.percentage > this.thresholds.memory.warning) {
      this.createAlert({
        level: 'warning',
        type: 'memory',
        message: `High memory usage: ${metrics.memory.percentage.toFixed(1)}%`,
        value: metrics.memory.percentage,
        threshold: this.thresholds.memory.warning
      });
    }

    // Disk usage check
    if (metrics.disk.percentage > this.thresholds.disk.critical) {
      this.createAlert({
        level: 'critical',
        type: 'disk',
        message: `Critical disk usage: ${metrics.disk.percentage.toFixed(1)}%`,
        value: metrics.disk.percentage,
        threshold: this.thresholds.disk.critical
      });
    } else if (metrics.disk.percentage > this.thresholds.disk.warning) {
      this.createAlert({
        level: 'warning',
        type: 'disk',
        message: `High disk usage: ${metrics.disk.percentage.toFixed(1)}%`,
        value: metrics.disk.percentage,
        threshold: this.thresholds.disk.warning
      });
    }
  }

  /**
   * Check stream performance thresholds
   */
  private checkStreamThresholds(metrics: StreamPerformanceMetrics): void {
    const { streamKey } = metrics;

    // Buffer health check
    if (metrics.bufferHealth < this.thresholds.bufferHealth.critical) {
      this.createAlert({
        level: 'critical',
        type: 'stream',
        message: `Critical buffer health for stream ${streamKey}: ${metrics.bufferHealth.toFixed(1)}%`,
        streamKey,
        value: metrics.bufferHealth,
        threshold: this.thresholds.bufferHealth.critical
      });
    } else if (metrics.bufferHealth < this.thresholds.bufferHealth.warning) {
      this.createAlert({
        level: 'warning',
        type: 'stream',
        message: `Low buffer health for stream ${streamKey}: ${metrics.bufferHealth.toFixed(1)}%`,
        streamKey,
        value: metrics.bufferHealth,
        threshold: this.thresholds.bufferHealth.warning
      });
    }

    // Viewer count check (for scaling alerts)
    if (metrics.viewers > this.thresholds.viewers.critical) {
      this.createAlert({
        level: 'critical',
        type: 'stream',
        message: `High viewer count for stream ${streamKey}: ${metrics.viewers}`,
        streamKey,
        value: metrics.viewers,
        threshold: this.thresholds.viewers.critical
      });
    } else if (metrics.viewers > this.thresholds.viewers.warning) {
      this.createAlert({
        level: 'warning',
        type: 'stream',
        message: `Elevated viewer count for stream ${streamKey}: ${metrics.viewers}`,
        streamKey,
        value: metrics.viewers,
        threshold: this.thresholds.viewers.warning
      });
    }
  }

  /**
   * Create a new alert
   */
  private createAlert(alertData: Omit<PerformanceAlert, 'id' | 'timestamp' | 'resolved'>): void {
    const alertId = `${alertData.type}_${alertData.streamKey || 'system'}_${Date.now()}`;
    
    const alert: PerformanceAlert = {
      id: alertId,
      timestamp: Date.now(),
      resolved: false,
      ...alertData
    };

    this.alerts.set(alertId, alert);
    this.emit('alert', alert);
    
    logger.warn('Performance alert created:', alert);
  }

  /**
   * Store system metrics in history
   */
  private storeSystemMetrics(metrics: SystemMetrics): void {
    this.metricsHistory.push(metrics);
    
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.splice(0, this.metricsHistory.length - this.maxHistorySize);
    }
  }

  /**
   * Store stream metrics in history
   */
  private storeStreamMetrics(metrics: StreamPerformanceMetrics): void {
    const { streamKey } = metrics;
    
    if (!this.streamMetricsHistory.has(streamKey)) {
      this.streamMetricsHistory.set(streamKey, []);
    }
    
    const history = this.streamMetricsHistory.get(streamKey)!;
    history.push(metrics);
    
    if (history.length > this.maxHistorySize) {
      history.splice(0, history.length - this.maxHistorySize);
    }
  }

  /**
   * Get active streams from OvenMediaEngine
   */
  private async getActiveStreams(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 3000,
        headers: { 'Accept': 'application/xml' }
      });

      const streamMatches = response.data.match(/<stream>[\s\S]*?<\/stream>/g) || [];
      const activeStreams: string[] = [];

      for (const streamXml of streamMatches) {
        const nameMatch = streamXml.match(/<name>(.*?)<\/name>/);
        const isPublishing = streamXml.includes('<publishing>') || streamXml.includes('<active>');
        
        if (nameMatch && isPublishing) {
          activeStreams.push(nameMatch[1]);
        }
      }

      return activeStreams;
    } catch (error) {
      logger.error('Failed to get active streams:', error);
      return [];
    }
  }

  /**
   * Get stream statistics
   */
  private async getStreamStats(streamKey: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 2000,
        headers: { 'Accept': 'application/xml' }
      });

      // Parse stream-specific stats from XML
      const streamRegex = new RegExp(`<stream>.*?<name>${streamKey}</name>.*?</stream>`, 's');
      const streamMatch = response.data.match(streamRegex);
      
      if (streamMatch) {
        // Extract metrics from XML (simplified)
        const viewersMatch = streamMatch[0].match(/<nclients>(\d+)<\/nclients>/);
        const bwInMatch = streamMatch[0].match(/<bw_in>(\d+)<\/bw_in>/);
        const timeMatch = streamMatch[0].match(/<time>(\d+)<\/time>/);
        
        return {
          viewers: viewersMatch ? parseInt(viewersMatch[1]) : 0,
          bitrate: bwInMatch ? parseInt(bwInMatch[1]) : 0,
          fps: 30, // Would need more detailed parsing
          resolution: '1920x1080', // Would need more detailed parsing
          duration: timeMatch ? parseInt(timeMatch[1]) / 1000 : 0,
          droppedFrames: 0 // Would need more detailed parsing
        };
      }
      
      return null;
    } catch (error) {
      logger.error(`Failed to get stats for stream ${streamKey}:`, error);
      return null;
    }
  }

  /**
   * Clean up old resolved alerts
   */
  private async cleanupOldAlerts(): Promise<void> {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [alertId, alert] of this.alerts.entries()) {
      if (alert.resolved && (now - alert.timestamp) > maxAge) {
        this.alerts.delete(alertId);
      }
    }
  }

  /**
   * Get all active alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Get system metrics history
   */
  getSystemMetricsHistory(limit?: number): SystemMetrics[] {
    const history = this.metricsHistory;
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get stream metrics history
   */
  getStreamMetricsHistory(streamKey: string, limit?: number): StreamPerformanceMetrics[] {
    const history = this.streamMetricsHistory.get(streamKey) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      this.emit('alertResolved', alert);
      return true;
    }
    return false;
  }

  /**
   * Update performance thresholds
   */
  updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    Object.assign(this.thresholds, newThresholds);
    logger.info('Performance thresholds updated:', this.thresholds);
  }

  /**
   * Get current thresholds
   */
  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds };
  }
}

// Export singleton instance
export const performanceMonitorService = new PerformanceMonitorService();
