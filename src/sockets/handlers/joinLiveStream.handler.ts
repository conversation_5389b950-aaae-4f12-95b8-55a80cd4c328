import { Socket } from "socket.io";
import StreamView from "../../models/streamView/streamView.schema";
import mongoose from "mongoose";
import { liveStreamViewers, streamIO } from "../stream";
import { LIVE_STREAM_VIEWS } from "../../constants/socketMessage";
import LiveChat from "../../models/liveChatSchema/liveChatSchema";

/**
 * Called when a client joins a stream. we Have as set (liveStreamViewers)
 * we add user to Set and joins to socket room.
 */
export const joinLiveStreamHandler = async (
  socket: Socket,
  streamId: string
) => {
  try {
    if (!liveStreamViewers.has(streamId)) {
      liveStreamViewers.set(streamId, new Set());
    }

    liveStreamViewers.get(streamId)!.add(socket.id);
    socket.join(streamId);

    streamIO.to(streamId).emit(LIVE_STREAM_VIEWS.GET_VIEWS, {
      viewers: liveStreamViewers.get(streamId)?.size || 0,
    });

    const recentMessages = await LiveChat.find({ streamId: streamId })
      .select("-createdAt -updatedAt -__v")
      .populate({
        path: "user",
        select: "profilePicture username",
      })
      .limit(50);

    socket.emit("recentLiveChat", recentMessages);
  } catch (error) {
    console.log(error);
    console.error("Error joining live stream:", error);
  }
};

/**
 * this handler function is when user lefts the stream.
 * we remove the user from our set and update the count.
 * @param socket
 * @param streamId
 */
export const leaveLiveStreamHandler = async (
  socket: Socket,
  streamId: string
) => {
  console.log(`User ${socket.id} left the stream: ${streamId}`);
  if (liveStreamViewers.has(streamId)) {
    const viewers = liveStreamViewers.get(streamId)!;

    if (viewers.has(socket.id)) {
      viewers.delete(socket.id);
      console.log(`Viewer left stream ${streamId}, new count: ${viewers.size}`);

      if (streamIO) {
        streamIO.to(streamId).emit(LIVE_STREAM_VIEWS.GET_VIEWS, {
          viewers: viewers.size,
        });
      }

      if (viewers.size === 0) {
        liveStreamViewers.delete(streamId);
        console.log(`Stream ${streamId} removed from active viewers`);
      }
    }
  }
};

/**
 * Heartbeat mechanism which we can implement later for scalability and optimization
 * @param socket
 * @param streamId
 * @returns
 */
export async function heartbeatStreamHandler(socket: Socket, streamId: string) {
  try {
    const userId = socket.user?._id;
    if (!userId) {
      socket.emit("error", { message: "User not authenticated" });
      return;
    }

    // Convert streamId to a Mongoose ObjectId if needed
    const streamObjectId = new mongoose.Types.ObjectId(streamId);

    // Update the existing StreamView record's lastHeartbeat
    const now = new Date();
    const updatedView = await StreamView.findOneAndUpdate(
      { user: userId, stream: streamObjectId },
      {
        $set: {
          lastHeartbeat: now,
          lastUpdated: now,
        },
        // Optionally increment watch time if desired
        $inc: {
          minutesWatched: 0.25, // e.g., if your heartbeat is every 15s => 0.25 min
        },
      },
      { new: true }
    );

    if (!updatedView) {
      // If for some reason no record was found, you might handle it by
      // creating a new record or just ignoring it.
      socket.emit("error", {
        message: "No existing StreamView found to update heartbeat.",
      });
      return;
    }

    // Optionally recalc how many viewers are active, and broadcast
    const viewerCount = await getRealTimeViewerCount(streamId);
    // Broadcast to the room
    socket.to(streamId).emit("UPDATE_VIEW_COUNT", { viewerCount });
    // Also emit back to the user if needed
    socket.emit("UPDATE_VIEW_COUNT", { viewerCount });
  } catch (error) {
    console.error("heartbeatStreamHandler error:", error);
    socket.emit("error", { message: "Internal server error" });
  }
}

/**
 * function to get realtime count
 * @param streamId
 * @returns
 */
export async function getRealTimeViewerCount(
  streamId: string
): Promise<number> {
  const THIRTY_SECONDS_AGO = new Date(Date.now() - 30_000);

  const count = await StreamView.countDocuments({
    stream: streamId,
    lastHeartbeat: { $gte: THIRTY_SECONDS_AGO },
  });
  return count;
}

/**
 * Called when a client leaves a stream.
 */
export const leaveStreamHandler = async (socket: Socket, streamId: string) => {
  await StreamView.deleteOne({ streamId, socketId: socket.id });

  // Emit updated viewer count
  const count = await StreamView.countDocuments({ streamId });
  socket.emit("viewerCount", count);
  socket.to(streamId).emit("viewerCount", count);
  socket.leave(streamId);
};

export const liveChatMessageHandler = async (
  socket: Socket,
  streamId: string,
  message: string
) => {
  try {
    const chatMessage = new LiveChat({
      user: socket.user._id,
      streamId,
      message,
    });
    await chatMessage.save();
    const user = {
      _id: socket.user?._id,
      username: socket.user?.username,
      profilePicture: socket.user?.profilePicture,
    };

    streamIO.to(streamId).emit("newLiveChatMessage", {
      user,
      message,
      timestamp: new Date(),
    });
  } catch (error) {
    // Error handling removed for production - using proper logger instead
  }
};
