import { Server } from "socket.io";
import authenticateSocket from "../middlewares/authenticate-socket";

import { likeStreamHandler } from "./handlers/likeStream.handler";
import {
  commentStreamHandler,
  deleteCommentFromStreamHandler,
} from "./handlers/commentStream.handler";
import { viewStreamHandler } from "./handlers/viewStream.handler";
import {
  COMMENT_EMITS,
  FOLLOW_EMITS,
  LIKE_EMITS,
  LIVE_STREAM_VIEWS,
  VIEW_EMITS,
} from "../constants/socketMessage";
import { followUserHandler } from "./handlers/follow.handler";
import { Namespace } from "socket.io";
import {
  heartbeatStreamHandler,
  joinLiveStreamHandler,
  leaveLiveStreamHandler,
  liveChatMessageHandler,
} from "./handlers/joinLiveStream.handler";

export let streamIO: Namespace;

/**
 * currently I am using Map we, will later move on to redis instance.
 */
export const liveStreamViewers = new Map<string, Set<string>>();

const streamSocket = (io: Server) => {
  streamIO = io.of("/stream");

  streamIO.use(authenticateSocket);

  streamIO.on("connection", (socket) => {
    // Removed console.log for production - using proper logger instead

    /**
     * Like stream event
     */
    socket.on(
      LIKE_EMITS.LIKE_EMIT,
      async (streamId: string, action: "like" | "unlike") =>
        likeStreamHandler(socket, streamId, action)
    );

    /**
     * Joining the live-stream event different from regular views
     */
    socket.on(LIVE_STREAM_VIEWS.VIEW, async (streamId: string) =>
      joinLiveStreamHandler(socket, streamId)
    );

    /**
     * Emit to fire when user leaves the streamas
     */
    socket.on(LIVE_STREAM_VIEWS.LEAVE, async (streamId: string) =>
      leaveLiveStreamHandler(socket, streamId)
    );

    /**
     * Comment stream event
     */
    socket.on(
      COMMENT_EMITS.ADD_COMMENT,
      async (streamId: string, content?: string, replyTo?: string) => {
        commentStreamHandler(socket, streamId, content, replyTo);
      }
    );

    socket.on("liveChatMessage", async (streamId: string, message: string) => {
      liveChatMessageHandler(socket, streamId, message);
    });

    /**
     * Delete comment
     */
    socket.on(COMMENT_EMITS.DELETE_COMMENT, async (commentId: string) => {
      deleteCommentFromStreamHandler(socket, commentId);
    });

    /**
     * View stream event
     */
    socket.on(VIEW_EMITS.VIEW_STREAM, async (streamId: string) =>
      viewStreamHandler(socket, streamId)
    );

    /**
     * Socket event for Following/unfollowing user
     */
    socket.on(FOLLOW_EMITS.FOLLOW_ACTION, async (targetUserId: string) =>
      followUserHandler(socket, targetUserId)
    );

    socket.on("disconnect", () => {
      console.log(`User disconnected: ${socket.id}`);
      liveStreamViewers.forEach((viewers, streamId) => {
        if (viewers.has(socket.id)) {
          leaveLiveStreamHandler(socket, streamId);
        }
      });
    });
  });
};

export default streamSocket;
