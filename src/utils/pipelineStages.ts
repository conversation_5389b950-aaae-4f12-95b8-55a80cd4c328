import mongoose, { PipelineStage } from "mongoose";

export const FollowSearchStage = (search: string): any[] => {
  if (!search) return [];

  return [
    {
      $match: {
        $or: [
          { username: { $regex: search, $options: "i" } },
          { displayName: { $regex: search, $options: "i" } },
        ],
      },
    },
  ];
};

export const buildProjectStage = (type: "stream" | "short", userId: string) => {
  if (type === "stream") {
    return {
      savedAt: 1,
      "itemDetails._id": 1,
      "itemDetails.title": 1,
      "itemDetails.description": 1,
      "itemDetails.cover": 1,
      "itemDetails.transcodedUrl": 1,
      "itemDetails.vodUrls": 1,
      "itemDetails.createdAt": 1,
      "itemDetails.videoLength": 1,
      "itemDetails.tags": 1,

      "itemDetails.viewsCount": { $size: "$itemDetails.views" },
      "itemDetails.likesCount": { $size: "$itemDetails.likes" },
      "itemDetails.commentCount": { $size: "$itemDetails.comments" },
      "itemDetails.sharesCount": { $size: "$itemDetails.shares" },
      "itemDetails.isLive": 1,
      "itemDetails.isLiked": {
        $cond: {
          if: { $gt: [{ $size: { $ifNull: ["$itemDetails.likes", []] } }, 0] },
          then: {
            $in: [new mongoose.Types.ObjectId(userId), "$itemDetails.likes"],
          },
          else: false,
        },
      },
      "itemDetails.creator": {
        _id: "$itemDetails.creatorDetails._id",
        username: "$itemDetails.creatorDetails.username",
        bio: "$itemDetails.creatorDetails.bio",
        displayName: "$itemDetails.creatorDetails.displayName",
        profilePicture: "$itemDetails.creatorDetails.profilePicture",
        isFollowing: {
          $cond: {
            if: {
              $gt: [
                {
                  $size: {
                    $ifNull: ["$itemDetails.creatorDetails.followers", []],
                  },
                },
                0,
              ],
            },
            then: {
              $in: [
                new mongoose.Types.ObjectId(userId),
                "$itemDetails.creatorDetails.followers",
              ],
            },
            else: false,
          },
        },
      },
    };
  }

  if (type === "short") {
    return {
      savedAt: 1,
      "itemDetails._id": 1,
      "itemDetails.description": 1,
      "itemDetails.thumbnailUrl": 1,
      "itemDetails.videoUrl": 1,
      "itemDetails.audioDetails": 1,
      "itemDetails.tags": 1,
      "itemDetails.createdAt": 1,

      "itemDetails.likesCount": {
        $size: {
          $cond: {
            if: { $isArray: "$itemDetails.likes" },
            then: "$itemDetails.likes",
            else: [],
          },
        },
      },
      "itemDetails.commentCount": {
        $size: {
          $cond: {
            if: { $isArray: "$itemDetails.comments" },
            then: "$itemDetails.comments",
            else: [],
          },
        },
      },

      "itemDetails.sharesCount": "$itemDetails.shares",

      "itemDetails.isLiked": {
        $cond: {
          if: {
            $in: [
              new mongoose.Types.ObjectId(userId),
              { $ifNull: ["$itemDetails.likes", []] },
            ],
          },
          then: true,
          else: false,
        },
      },
    };
  }

  return {};
};


export const getNotInterestedFilterStages = (
  userId: string,
  contentType: "stream" | "short"
): PipelineStage[] => {
  return [
    {
      $lookup: {
        from: "notinteresteds",
        let: { contentId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$contentId", "$$contentId"] },
                  { $eq: ["$userId", new mongoose.Types.ObjectId(userId)] },
                  { $eq: ["$contentType", contentType] },
                ],
              },
            },
          },
        ],
        as: "notInterested",
      },
    },
    {
      $match: {
        notInterested: { $eq: [] },
      },
    },
  ];
};
