#!/usr/bin/env node

/**
 * Test script for LLHLS webhook integration
 * 
 * This script simulates OvenMediaEngine AdmissionWebhook calls
 * to test the new LLHLS integration endpoint.
 * 
 * Usage:
 *   node test-llhls-webhook.js [start|end] [streamKey]
 * 
 * Examples:
 *   node test-llhls-webhook.js start user123_stream456
 *   node test-llhls-webhook.js end user123_stream456
 */

const http = require('http');

const API_BASE_URL = 'http://localhost:8081';
const WEBHOOK_SECRET = 'ome-webhook-secret-2024';

function sendWebhook(action, streamKey) {
  const status = action === 'start' ? 'opening' : 'closing';
  
  const payload = {
    request: {
      application: 'app',
      stream: {
        name: streamKey
      },
      status: status
    },
    secret: WEBHOOK_SECRET
  };

  const postData = JSON.stringify(payload);

  const options = {
    hostname: 'localhost',
    port: 8081,
    path: '/v1/api/stream/live',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'X-OME-Secret': WEBHOOK_SECRET,
      'User-Agent': 'OvenMediaEngine/1.0'
    }
  };

  console.log(`\n🚀 Sending LLHLS webhook: ${action} for stream ${streamKey}`);
  console.log('📤 Payload:', JSON.stringify(payload, null, 2));

  const req = http.request(options, (res) => {
    console.log(`\n📥 Response Status: ${res.statusCode}`);
    console.log('📥 Response Headers:', res.headers);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('📥 Response Body:', responseData);
      
      try {
        const parsed = JSON.parse(responseData);
        if (parsed.success) {
          console.log('✅ Webhook processed successfully!');
        } else {
          console.log('❌ Webhook failed:', parsed.message);
        }
      } catch (e) {
        console.log('⚠️  Non-JSON response received');
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ Request failed:', e.message);
  });

  req.write(postData);
  req.end();
}

function showUsage() {
  console.log(`
🧪 LLHLS Webhook Test Script

Usage:
  node test-llhls-webhook.js [start|end] [streamKey]

Examples:
  node test-llhls-webhook.js start user123_stream456
  node test-llhls-webhook.js end user123_stream456

This script simulates OvenMediaEngine AdmissionWebhook calls to test
the new LLHLS integration endpoint at /v1/api/stream/live

Make sure your Node.js server is running on port 8081 before testing.
`);
}

// Main execution
const args = process.argv.slice(2);

if (args.length !== 2) {
  showUsage();
  process.exit(1);
}

const [action, streamKey] = args;

if (!['start', 'end'].includes(action)) {
  console.error('❌ Action must be either "start" or "end"');
  showUsage();
  process.exit(1);
}

if (!streamKey) {
  console.error('❌ Stream key is required');
  showUsage();
  process.exit(1);
}

// Accept both JWT tokens and userId_streamId format
if (!streamKey.includes('_') && !streamKey.includes('.')) {
  console.error('❌ Stream key must be either JWT token or in format "userId_streamId"');
  showUsage();
  process.exit(1);
}

sendWebhook(action, streamKey);
